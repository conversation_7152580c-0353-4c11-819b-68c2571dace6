#!/usr/bin/env python3
"""
Script para testar se o scraper do Drogasil está sendo importado corretamente
"""

import sys
import traceback

def test_imports():
    """Testa todas as importações necessárias"""
    print("🧪 Testando importações...")
    
    try:
        print("1. Testando importação do DrogasilScraper...")
        from services.drogasil_scraper import DrogasilScraper
        print("   ✅ DrogasilScraper importado com sucesso")
        
        print("2. Testando criação de instância...")
        scraper = DrogasilScraper()
        print("   ✅ Instância criada com sucesso")
        
        print("3. Testando métodos básicos...")
        eans = scraper.get_eans()
        print(f"   ✅ EANs padrão: {len(eans)} códigos")
        
        urls = scraper.get_urls_to_scrape()
        print(f"   ✅ URLs geradas: {len(urls)} URLs")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro na importação: {e}")
        print(f"   📋 Traceback:")
        traceback.print_exc()
        return False

def test_main_registration():
    """Testa se o scraper está registrado no main.py"""
    print("\n🔧 Testando registro no sistema...")
    
    try:
        print("1. Importando ScrapingManager...")
        from main import ScrapingManager
        print("   ✅ ScrapingManager importado")
        
        print("2. Criando manager...")
        manager = ScrapingManager()
        print("   ✅ Manager criado")
        
        print("3. Verificando scrapers registrados...")
        scrapers = manager.get_available_scrapers()
        print(f"   📋 Scrapers disponíveis: {scrapers}")
        
        if 'drogasil' in scrapers:
            print("   ✅ Drogasil está registrado!")
            return True
        else:
            print("   ❌ Drogasil NÃO está registrado!")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro no registro: {e}")
        print(f"   📋 Traceback:")
        traceback.print_exc()
        return False

def test_scraper_execution():
    """Testa execução básica do scraper"""
    print("\n🚀 Testando execução do scraper...")
    
    try:
        from services.drogasil_scraper import DrogasilScraper
        
        print("1. Criando scraper com EAN de teste...")
        test_ean = "7891991010016"  # Paracetamol
        scraper = DrogasilScraper(eans=[test_ean])
        print(f"   ✅ Scraper criado com EAN: {test_ean}")
        
        print("2. Testando geração de URLs...")
        urls = scraper.get_urls_to_scrape()
        print(f"   ✅ URL gerada: {urls[0]}")
        
        print("3. Testando métodos auxiliares...")
        scraper.add_ean("1234567890123")
        current_eans = scraper.get_eans()
        print(f"   ✅ EANs após adição: {len(current_eans)} códigos")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro na execução: {e}")
        print(f"   📋 Traceback:")
        traceback.print_exc()
        return False

def main():
    """Função principal de teste"""
    print("🔍 TESTE DE IMPORTAÇÃO - DROGASIL SCRAPER")
    print("=" * 50)
    
    # Teste 1: Importações
    import_ok = test_imports()
    
    # Teste 2: Registro no sistema
    registration_ok = test_main_registration()
    
    # Teste 3: Execução básica
    execution_ok = test_scraper_execution()
    
    # Resumo
    print("\n" + "=" * 50)
    print("📊 RESUMO DOS TESTES:")
    print(f"   Importação: {'✅' if import_ok else '❌'}")
    print(f"   Registro: {'✅' if registration_ok else '❌'}")
    print(f"   Execução: {'✅' if execution_ok else '❌'}")
    
    if all([import_ok, registration_ok, execution_ok]):
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("   O scraper do Drogasil está funcionando corretamente.")
        print("\n🚀 Para executar:")
        print("   python main.py scrape --scraper drogasil")
        return 0
    else:
        print("\n❌ ALGUNS TESTES FALHARAM!")
        print("   Verifique os erros acima.")
        
        if not import_ok:
            print("\n🔧 POSSÍVEIS SOLUÇÕES:")
            print("   1. Verifique se o arquivo services/drogasil_scraper.py existe")
            print("   2. Verifique se não há erros de sintaxe no arquivo")
            print("   3. Verifique se todas as dependências estão instaladas")
        
        if not registration_ok:
            print("\n🔧 POSSÍVEIS SOLUÇÕES:")
            print("   1. Verifique se a importação está correta no main.py")
            print("   2. Verifique se o scraper está sendo registrado em register_scrapers()")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
