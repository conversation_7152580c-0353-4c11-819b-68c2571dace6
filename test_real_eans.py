#!/usr/bin/env python3
"""
Teste com EANs reais de produtos farmacêuticos
"""

import asyncio
from services.drogasil_scraper import DrogasilScraper

async def test_real_eans():
    """Testa EANs de produtos farmacêuticos reais"""
    
    # EANs de produtos farmacêuticos comuns
    real_eans = [
        "7896658003004",  # Dipirona Sódica
        "7896658003011",  # Paracetamol
        "7896658003028",  # Ibuprofeno
        "7891317000015",  # <PERSON><PERSON><PERSON>
        "7891317000022",  # Vitamina C
    ]
    
    for ean in real_eans:
        print(f"\n🔍 Testando EAN: {ean}")
        print("-" * 40)
        
        scraper = DrogasilScraper(eans=[ean])
        
        try:
            async with scraper:
                url = f"https://www.drogasil.com.br/search?w={ean}"
                print(f"📍 URL: {url}")
                
                await scraper.navigate_to_page(url)
                await scraper.page.wait_for_timeout(10000)  # Aguardar JavaScript
                
                # Verificar título
                title = await scraper.page.title()
                print(f"📄 Título: {title}")
                
                # Verificar se há produtos
                page_text = await scraper.page.inner_text('body')
                
                if 'nenhum resultado' in page_text.lower():
                    print("❌ Nenhum resultado encontrado")
                elif 'R$' in page_text:
                    print("✅ Produtos encontrados (contém preços)!")
                    
                    # Tentar extrair dados
                    products = await scraper.scrape()
                    print(f"📦 Produtos extraídos: {len(products)}")
                    
                    if products:
                        for i, product in enumerate(products[:3], 1):  # Mostrar apenas 3
                            print(f"   {i}. {product.get('product_name', 'N/A')}")
                            print(f"      Preço: R$ {product.get('price', 'N/A')}")
                        break  # Se encontrou produtos, para aqui
                else:
                    print("⚠️ Status desconhecido")
                    
        except Exception as e:
            print(f"❌ Erro: {e}")

if __name__ == "__main__":
    asyncio.run(test_real_eans())
