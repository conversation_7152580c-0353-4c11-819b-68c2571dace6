#!/usr/bin/env python3
"""
Script para debugar o scraper do Drogasil e descobrir os seletores corretos
"""

import asyncio
from services.drogasil_scraper import DrogasilScraper

async def debug_drogasil():
    """Debug do scraper do Drogasil"""
    print("🔍 Debugando scraper do Drogasil...")
    
    # EAN de teste (Coca-Cola)
    test_ean = "7891000100103"
    url = f"https://www.drogasil.com.br/search?w={test_ean}"
    
    scraper = DrogasilScraper(eans=[test_ean])
    
    try:
        async with scraper:
            print(f"📍 Navegando para: {url}")
            await scraper.navigate_to_page(url)
            
            # Aguardar página carregar
            await scraper.page.wait_for_timeout(5000)
            
            # Verificar título
            title = await scraper.page.title()
            print(f"📄 Título da página: {title}")
            
            # Verificar se há elementos de produto
            possible_selectors = [
                '.product',
                '.item',
                '[data-testid*="product"]',
                '.card',
                '.result',
                '.search-result',
                '.product-card',
                '.product-item',
                '.showcase-item'
            ]
            
            print("\n🔍 Testando seletores possíveis:")
            for selector in possible_selectors:
                elements = await scraper.page.query_selector_all(selector)
                if elements:
                    print(f"   ✅ {selector}: {len(elements)} elementos encontrados")
                else:
                    print(f"   ❌ {selector}: nenhum elemento")
            
            # Verificar se há mensagem de "não encontrado"
            no_results_selectors = [
                '.no-results',
                '.empty',
                '.not-found',
                '[class*="empty"]',
                '[class*="not-found"]'
            ]
            
            print("\n🚫 Verificando mensagens de 'não encontrado':")
            for selector in no_results_selectors:
                elements = await scraper.page.query_selector_all(selector)
                if elements:
                    text = await elements[0].inner_text()
                    print(f"   ⚠️ {selector}: {text}")
            
            # Salvar screenshot para análise
            await scraper.page.screenshot(path="debug_drogasil.png")
            print("\n📸 Screenshot salvo como: debug_drogasil.png")
            
            # Salvar HTML da página
            content = await scraper.page.content()
            with open("debug_drogasil.html", "w", encoding="utf-8") as f:
                f.write(content)
            print("💾 HTML salvo como: debug_drogasil.html")
            
            print(f"\n📊 Página carregada: {len(content)} caracteres")
            
    except Exception as e:
        print(f"❌ Erro no debug: {e}")

if __name__ == "__main__":
    asyncio.run(debug_drogasil())
