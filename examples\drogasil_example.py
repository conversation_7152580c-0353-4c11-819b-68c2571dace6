#!/usr/bin/env python3
"""
Exemplo de uso do scraper do Drogasil
Demonstra como buscar produtos por EAN
"""

import asyncio
import sys
import os

# Adiciona o diretório pai ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.drogasil_scraper import DrogasilScraper
from utils.logger import get_logger

logger = get_logger("drogasil_example")

async def example_single_ean():
    """Exemplo: buscar um produto específico por EAN"""
    print("🔍 Exemplo 1: Busca por EAN específico")
    print("-" * 40)
    
    # EAN de exemplo (Paracetamol)
    ean = "7891991010016"
    
    # Criar scraper com EAN específico
    scraper = DrogasilScraper(eans=[ean])
    
    try:
        # Executar scraping
        await scraper.run_scraping()
        print(f"✅ Busca por EAN {ean} concluída!")
        
    except Exception as e:
        print(f"❌ Erro na busca: {e}")

async def example_multiple_eans():
    """Exemplo: buscar múltiplos produtos por EAN"""
    print("\n🔍 Exemplo 2: Busca por múltiplos EANs")
    print("-" * 40)
    
    # Lista de EANs para buscar
    eans = [
        "7891991010016",  # Paracetamol
        "7891106001533",  # Dipirona
        "7891106001540",  # Ibuprofeno
        "7891000100103",  # Coca-Cola
    ]
    
    # Criar scraper com múltiplos EANs
    scraper = DrogasilScraper(eans=eans)
    
    try:
        # Executar scraping
        await scraper.run_scraping()
        print(f"✅ Busca por {len(eans)} EANs concluída!")
        
    except Exception as e:
        print(f"❌ Erro na busca: {e}")

async def example_dynamic_eans():
    """Exemplo: adicionar EANs dinamicamente"""
    print("\n🔍 Exemplo 3: Adicionando EANs dinamicamente")
    print("-" * 40)
    
    # Criar scraper vazio
    scraper = DrogasilScraper(eans=[])
    
    # Adicionar EANs um por um
    scraper.add_ean("7891991010016")  # Paracetamol
    scraper.add_ean("7891106001533")  # Dipirona
    
    # Verificar EANs configurados
    current_eans = scraper.get_eans()
    print(f"EANs configurados: {current_eans}")
    
    try:
        # Executar scraping
        await scraper.run_scraping()
        print("✅ Busca dinâmica concluída!")
        
    except Exception as e:
        print(f"❌ Erro na busca: {e}")

async def example_manual_scraping():
    """Exemplo: scraping manual com controle detalhado"""
    print("\n🔍 Exemplo 4: Scraping manual detalhado")
    print("-" * 40)
    
    ean = "7891991010016"  # Paracetamol
    scraper = DrogasilScraper(eans=[ean])
    
    try:
        # Usar context manager para controle manual
        async with scraper:
            # Navegar para página de busca
            search_url = f"https://www.drogasil.com.br/search?w={ean}"
            await scraper.navigate_to_page(search_url)
            
            # Extrair dados
            products = await scraper.scrape()
            
            # Mostrar resultados
            print(f"Produtos encontrados: {len(products)}")
            for i, product in enumerate(products, 1):
                print(f"\n📦 Produto {i}:")
                print(f"   Nome: {product.get('product_name')}")
                print(f"   Preço: R$ {product.get('price', 'N/A')}")
                print(f"   Marca: {product.get('brand', 'N/A')}")
                print(f"   Disponível: {'Sim' if product.get('availability') else 'Não'}")
                print(f"   URL: {product.get('url', 'N/A')}")
                
                # Salvar no banco
                scraper.save_scraped_data(product)
        
        print("✅ Scraping manual concluído!")
        
    except Exception as e:
        print(f"❌ Erro no scraping manual: {e}")

def example_ean_validation():
    """Exemplo: validação de EANs"""
    print("\n🔍 Exemplo 5: Validação de EANs")
    print("-" * 40)
    
    # EANs para testar
    test_eans = [
        "7891991010016",    # Válido - 13 dígitos
        "789199101001",     # Inválido - 12 dígitos
        "78919910100123",   # Inválido - 14 dígitos
        "abc123def456",     # Inválido - não numérico
        "7891106001533",    # Válido - 13 dígitos
    ]
    
    def validate_ean(ean: str) -> bool:
        """Validação básica de EAN"""
        return ean.isdigit() and len(ean) == 13
    
    valid_eans = []
    for ean in test_eans:
        is_valid = validate_ean(ean)
        status = "✅ Válido" if is_valid else "❌ Inválido"
        print(f"   {ean}: {status}")
        
        if is_valid:
            valid_eans.append(ean)
    
    print(f"\nEANs válidos para busca: {valid_eans}")
    return valid_eans

async def main():
    """Função principal com todos os exemplos"""
    print("🏥 EXEMPLOS DE USO - SCRAPER DROGASIL")
    print("=" * 50)
    
    # Exemplo 1: EAN único
    await example_single_ean()
    
    # Exemplo 2: Múltiplos EANs
    await example_multiple_eans()
    
    # Exemplo 3: EANs dinâmicos
    await example_dynamic_eans()
    
    # Exemplo 4: Scraping manual
    await example_manual_scraping()
    
    # Exemplo 5: Validação de EANs
    valid_eans = example_ean_validation()
    
    print("\n" + "=" * 50)
    print("📋 RESUMO DOS EXEMPLOS:")
    print("✅ Busca por EAN específico")
    print("✅ Busca por múltiplos EANs")
    print("✅ Adição dinâmica de EANs")
    print("✅ Scraping manual detalhado")
    print("✅ Validação de EANs")
    
    print("\n🚀 COMO USAR NO SEU CÓDIGO:")
    print("""
# Importar o scraper
from services.drogasil_scraper import DrogasilScraper

# Criar scraper com EANs
scraper = DrogasilScraper(eans=["7891991010016", "7891106001533"])

# Executar scraping
await scraper.run_scraping()

# Ou usar via linha de comando:
# python main.py scrape --scraper drogasil
""")

if __name__ == "__main__":
    # Executar exemplos
    asyncio.run(main())
