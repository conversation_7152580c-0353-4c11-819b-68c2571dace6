from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>
from sqlalchemy.orm import Session
from database.models import ScrapedD<PERSON>, ScrapingJob
from database.connection import DatabaseManager
from utils.logger import get_logger
from config.settings import settings
import asyncio
import time
from datetime import datetime

class BaseScraper(ABC):
    """Classe base para todos os scrapers"""
    
    def __init__(self, site_name: str):
        self.site_name = site_name
        self.logger = get_logger(f"scraper.{site_name}")
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.job_id: Optional[int] = None
        
    async def __aenter__(self):
        """Context manager entry"""
        await self.setup_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        await self.cleanup()
    
    async def setup_browser(self):
        """Configura o browser Playwright"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=settings.PLAYWRIGHT_HEADLESS
            )
            self.page = await self.browser.new_page()
            
            # Configurações da página
            await self.page.set_viewport_size({"width": 1920, "height": 1080})
            await self.page.set_extra_http_headers(self.get_headers())
            
            self.logger.info(f"Browser configurado para {self.site_name}")
            
        except Exception as e:
            self.logger.error(f"Erro ao configurar browser: {e}")
            raise
    
    async def cleanup(self):
        """Limpa recursos do browser"""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            self.logger.info(f"Recursos limpos para {self.site_name}")
        except Exception as e:
            self.logger.error(f"Erro ao limpar recursos: {e}")
    
    def get_headers(self) -> Dict[str, str]:
        """Retorna headers padrão para requisições"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    async def navigate_to_page(self, url: str, wait_for: str = None):
        """Navega para uma página específica"""
        try:
            await self.page.goto(url, timeout=settings.PLAYWRIGHT_TIMEOUT)
            
            if wait_for:
                await self.page.wait_for_selector(wait_for, timeout=settings.PLAYWRIGHT_TIMEOUT)
            
            await asyncio.sleep(settings.SCRAPING_DELAY)
            self.logger.info(f"Navegou para: {url}")
            
        except Exception as e:
            self.logger.error(f"Erro ao navegar para {url}: {e}")
            raise
    
    async def retry_operation(self, operation, max_retries: int = None):
        """Executa uma operação com retry"""
        max_retries = max_retries or settings.MAX_RETRIES
        
        for attempt in range(max_retries):
            try:
                return await operation()
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                self.logger.warning(f"Tentativa {attempt + 1} falhou: {e}")
                await asyncio.sleep(2 ** attempt)  # Backoff exponencial
    
    def create_scraping_job(self) -> int:
        """Cria um job de scraping no banco"""
        db = DatabaseManager.get_sync_session()
        try:
            job = ScrapingJob(
                site_name=self.site_name,
                status="running",
                start_time=datetime.utcnow()
            )
            db.add(job)
            db.commit()
            db.refresh(job)
            self.job_id = job.id
            self.logger.info(f"Job de scraping criado: {job.id}")
            return job.id
        finally:
            db.close()
    
    def update_scraping_job(self, status: str, items_scraped: int = 0, 
                           errors_count: int = 0, error_message: str = None):
        """Atualiza o status do job de scraping"""
        if not self.job_id:
            return
            
        db = DatabaseManager.get_sync_session()
        try:
            job = db.query(ScrapingJob).filter(ScrapingJob.id == self.job_id).first()
            if job:
                job.status = status
                job.items_scraped = items_scraped
                job.errors_count = errors_count
                job.error_message = error_message
                if status in ["completed", "failed"]:
                    job.end_time = datetime.utcnow()
                db.commit()
                self.logger.info(f"Job {self.job_id} atualizado: {status}")
        finally:
            db.close()
    
    def save_scraped_data(self, data: Dict[str, Any]):
        """Salva dados extraídos no banco"""
        db = DatabaseManager.get_sync_session()
        try:
            scraped_item = ScrapedData(
                site_name=self.site_name,
                **data
            )
            db.add(scraped_item)
            db.commit()
            self.logger.info(f"Dados salvos: {data.get('product_name', 'Item')}")
        except Exception as e:
            self.logger.error(f"Erro ao salvar dados: {e}")
            db.rollback()
        finally:
            db.close()
    
    @abstractmethod
    async def scrape(self) -> List[Dict[str, Any]]:
        """Método principal de scraping - deve ser implementado por cada scraper"""
        pass
    
    @abstractmethod
    def get_urls_to_scrape(self) -> List[str]:
        """Retorna lista de URLs para fazer scraping"""
        pass
    
    async def run_scraping(self):
        """Executa o processo completo de scraping"""
        job_id = self.create_scraping_job()
        items_scraped = 0
        errors_count = 0
        
        try:
            async with self:
                urls = self.get_urls_to_scrape()
                self.logger.info(f"Iniciando scraping de {len(urls)} URLs")
                
                for url in urls:
                    try:
                        await self.navigate_to_page(url)
                        data_list = await self.scrape()
                        
                        for data in data_list:
                            self.save_scraped_data(data)
                            items_scraped += 1
                            
                    except Exception as e:
                        errors_count += 1
                        self.logger.error(f"Erro ao processar {url}: {e}")
                
                self.update_scraping_job("completed", items_scraped, errors_count)
                self.logger.info(f"Scraping concluído: {items_scraped} itens, {errors_count} erros")
                
        except Exception as e:
            self.update_scraping_job("failed", items_scraped, errors_count, str(e))
            self.logger.error(f"Falha no scraping: {e}")
            raise
