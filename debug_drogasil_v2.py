#!/usr/bin/env python3
"""
Script melhorado para debugar o scraper do Drogasil
Aguarda o JavaScript carregar os produtos
"""

import asyncio
from services.drogasil_scraper import DrogasilScraper

async def debug_drogasil_v2():
    """Debug melhorado do scraper do Drogasil"""
    print("🔍 Debugando scraper do Drogasil (v2 - aguardando JavaScript)...")
    
    # EAN de teste (Coca-Cola)
    test_ean = "7891000100103"
    url = f"https://www.drogasil.com.br/search?w={test_ean}"
    
    scraper = DrogasilScraper(eans=[test_ean])
    
    try:
        async with scraper:
            print(f"📍 Navegando para: {url}")
            await scraper.navigate_to_page(url)
            
            # Aguardar mais tempo para o JavaScript carregar
            print("⏳ Aguardando JavaScript carregar produtos...")
            await scraper.page.wait_for_timeout(10000)  # 10 segundos
            
            # Verificar título
            title = await scraper.page.title()
            print(f"📄 Título da página: {title}")
            
            # Aguardar especificamente por elementos que podem aparecer
            print("🔍 Aguardando elementos de produto aparecerem...")
            
            # Tentar aguardar por diferentes seletores possíveis
            selectors_to_wait = [
                'div[data-testid*="product"]',
                '[class*="product"]',
                '[class*="item"]',
                '.card',
                'article',
                '[role="article"]'
            ]
            
            product_found = False
            for selector in selectors_to_wait:
                try:
                    print(f"   Tentando aguardar: {selector}")
                    await scraper.page.wait_for_selector(selector, timeout=5000)
                    print(f"   ✅ Encontrado: {selector}")
                    product_found = True
                    break
                except:
                    print(f"   ❌ Timeout: {selector}")
                    continue
            
            if not product_found:
                print("⚠️ Nenhum elemento de produto encontrado, tentando seletores genéricos...")
            
            # Aguardar mais um pouco
            await scraper.page.wait_for_timeout(5000)
            
            # Verificar se há elementos de produto agora
            all_possible_selectors = [
                'div[data-testid*="product"]',
                '[data-testid*="item"]',
                '[class*="product"]',
                '[class*="item"]',
                '[class*="card"]',
                'article',
                '[role="article"]',
                'div[class*="showcase"]',
                'div[class*="result"]',
                'div[class*="search"]'
            ]
            
            print("\n🔍 Testando seletores após aguardar JavaScript:")
            found_elements = []
            
            for selector in all_possible_selectors:
                elements = await scraper.page.query_selector_all(selector)
                if elements:
                    print(f"   ✅ {selector}: {len(elements)} elementos encontrados")
                    found_elements.append((selector, len(elements)))
                else:
                    print(f"   ❌ {selector}: nenhum elemento")
            
            # Se encontrou elementos, tentar extrair dados de um
            if found_elements:
                print(f"\n📦 Tentando extrair dados do primeiro elemento encontrado...")
                best_selector = found_elements[0][0]  # Pega o primeiro que encontrou
                elements = await scraper.page.query_selector_all(best_selector)
                
                if elements:
                    first_element = elements[0]
                    
                    # Tentar extrair texto
                    try:
                        text_content = await first_element.inner_text()
                        print(f"   📝 Texto do elemento: {text_content[:200]}...")
                    except:
                        print("   ❌ Não foi possível extrair texto")
                    
                    # Tentar extrair HTML
                    try:
                        html_content = await first_element.inner_html()
                        print(f"   🏷️ HTML do elemento: {html_content[:200]}...")
                    except:
                        print("   ❌ Não foi possível extrair HTML")
            
            # Verificar se há mensagem de "não encontrado"
            no_results_texts = [
                "nenhum resultado",
                "não encontrado",
                "sem resultados",
                "produto não encontrado",
                "busca sem resultado"
            ]
            
            page_content = await scraper.page.content()
            page_text = await scraper.page.inner_text('body')
            
            print(f"\n🔍 Verificando mensagens de 'não encontrado':")
            for text in no_results_texts:
                if text.lower() in page_text.lower():
                    print(f"   ⚠️ Encontrado: '{text}'")
                else:
                    print(f"   ✅ Não encontrado: '{text}'")
            
            # Verificar se há preços na página
            if 'R$' in page_text:
                print(f"\n💰 Página contém preços (R$)")
                # Contar quantas vezes aparece R$
                price_count = page_text.count('R$')
                print(f"   📊 Encontradas {price_count} ocorrências de 'R$'")
            else:
                print(f"\n❌ Página NÃO contém preços")
            
            # Salvar screenshot e HTML atualizados
            await scraper.page.screenshot(path="debug_drogasil_v2.png")
            print("\n📸 Screenshot salvo como: debug_drogasil_v2.png")
            
            with open("debug_drogasil_v2.html", "w", encoding="utf-8") as f:
                f.write(page_content)
            print("💾 HTML salvo como: debug_drogasil_v2.html")
            
            print(f"\n📊 Página carregada: {len(page_content)} caracteres")
            print(f"📊 Texto da página: {len(page_text)} caracteres")
            
    except Exception as e:
        print(f"❌ Erro no debug: {e}")

if __name__ == "__main__":
    asyncio.run(debug_drogasil_v2())
