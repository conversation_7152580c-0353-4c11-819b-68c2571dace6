2025-06-03 22:24:52 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:24:52 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:24:53 | INFO     | __main__:run_api:83 - Iniciando API em 0.0.0.0:8000
2025-06-03 22:24:53 | INFO     | api.main:startup_event:42 - Banco de dados inicializado
2025-06-03 22:25:22 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:25:22 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:25:22 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:25:22 | INFO     | __main__:main:117 - <PERSON><PERSON><PERSON> inicializado com sucesso!
2025-06-03 22:25:23 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:25:23 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:25:23 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:25:23 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:25:23 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:25:23 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 1
2025-06-03 22:25:24 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:25:24 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:25:24 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:25:24 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:25:25 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:25:25 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:25:25 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:25:25 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:25:25 | INFO     | core.base_scraper:update_scraping_job:138 - Job 1 atualizado: completed
2025-06-03 22:25:25 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:25:25 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:25:25 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:25:25 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:25:25 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 2
2025-06-03 22:25:26 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:25:26 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:25:55 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:25:55 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:26:23 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=notebook: Timeout 30000ms exceeded.
2025-06-03 22:26:23 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=notebook: Timeout 30000ms exceeded.
2025-06-03 22:26:37 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:26:37 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:26:37 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:06 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 22:27:06 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 22:27:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:27:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:21 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=televisao
2025-06-03 22:27:21 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:21 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:28 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=geladeira
2025-06-03 22:27:28 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:28 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:34 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=micro-ondas
2025-06-03 22:27:34 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:34 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:34 | INFO     | core.base_scraper:update_scraping_job:138 - Job 2 atualizado: completed
2025-06-03 22:27:34 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:27:34 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:27:34 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:27:34 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:27:36 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:27:36 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:27:36 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:27:36 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:27:37 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:27:37 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:27:37 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:27:37 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:27:37 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:27:37 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 3
2025-06-03 22:27:37 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:27:37 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:27:38 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:27:38 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:27:38 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:27:38 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:27:39 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:27:39 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:27:39 | INFO     | core.base_scraper:update_scraping_job:138 - Job 3 atualizado: completed
2025-06-03 22:27:39 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:27:39 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:27:39 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:27:39 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:27:39 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 4
2025-06-03 22:27:39 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:27:39 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:27:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartphone
2025-06-03 22:27:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:52 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:27:52 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:52 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:57 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:27:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:06 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:28:06 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:28:06 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:13 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:28:13 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:28:13 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:23 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=televisao
2025-06-03 22:28:23 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:28:23 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:26 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=geladeira
2025-06-03 22:28:27 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:28:27 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:34 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:28:34 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:28:34 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:28:34 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:28:34 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:28:34 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 5
2025-06-03 22:28:34 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:28:34 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:28:35 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:28:35 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:28:35 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=micro-ondas
2025-06-03 22:28:35 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:28:35 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:35 | INFO     | core.base_scraper:update_scraping_job:138 - Job 4 atualizado: completed
2025-06-03 22:28:35 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:28:35 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:28:35 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:28:35 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:28:37 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:28:37 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:28:37 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:28:37 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:28:38 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:28:38 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:28:38 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:28:38 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:28:38 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:28:38 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 6
2025-06-03 22:28:38 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:28:38 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:28:39 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:28:39 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:28:39 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:28:39 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:28:40 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:28:40 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:28:40 | INFO     | core.base_scraper:update_scraping_job:138 - Job 6 atualizado: completed
2025-06-03 22:28:40 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:28:40 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:28:40 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:28:40 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:28:40 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 7
2025-06-03 22:28:40 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:28:40 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:29:09 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:29:09 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:29:15 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:29:15 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:29:15 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:29:34 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:29:34 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:29:34 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:29:36 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:29:36 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:29:36 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:29:36 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:29:36 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 8
2025-06-03 22:29:37 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:29:37 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:29:48 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:29:48 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:29:48 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:29:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:29:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:29:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:06 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:30:06 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:30:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:30:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:20 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:30:20 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:20 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:26 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=televisao: Timeout 30000ms exceeded.
2025-06-03 22:30:26 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=televisao: Timeout 30000ms exceeded.
2025-06-03 22:30:29 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:30:29 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:29 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:32 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=geladeira
2025-06-03 22:30:32 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:32 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:35 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:30:35 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:35 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:40 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=televisao
2025-06-03 22:30:40 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:40 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:45 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=micro-ondas
2025-06-03 22:30:45 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:45 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:45 | INFO     | core.base_scraper:update_scraping_job:138 - Job 7 atualizado: completed
2025-06-03 22:30:45 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 2 erros
2025-06-03 22:30:45 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:30:45 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:30:45 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:30:47 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:30:47 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:30:47 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:30:47 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:30:48 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:30:48 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:30:48 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:30:48 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:30:48 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:30:48 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 9
2025-06-03 22:30:49 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:30:49 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:30:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:30:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:30:48 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:30:48 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:30:48 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:30:48 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:30:48 | INFO     | core.base_scraper:update_scraping_job:138 - Job 9 atualizado: completed
2025-06-03 22:30:48 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:30:48 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:30:48 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:30:48 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:30:48 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 10
2025-06-03 22:30:49 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:30:49 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:30:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartphone
2025-06-03 22:30:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:31:05 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:05 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:13 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:31:13 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:13 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:21 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:31:21 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:21 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:27 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:31:27 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:27 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:29 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:31:29 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:31:29 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:31:29 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:31:29 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 11
2025-06-03 22:31:30 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:31:30 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:31:32 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=televisao
2025-06-03 22:31:33 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:33 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:38 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=geladeira
2025-06-03 22:31:38 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:38 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:40 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartphone
2025-06-03 22:31:40 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:40 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=micro-ondas
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:46 | INFO     | core.base_scraper:update_scraping_job:138 - Job 10 atualizado: completed
2025-06-03 22:31:46 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:31:46 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:31:46 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:31:46 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:31:46 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:31:46 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:31:47 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:31:47 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:31:47 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:31:47 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:31:47 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:31:47 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:31:47 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:31:47 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 12
2025-06-03 22:31:48 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:31:48 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:31:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:31:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:31:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:31:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:31:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:31:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:31:49 | INFO     | core.base_scraper:update_scraping_job:138 - Job 12 atualizado: completed
2025-06-03 22:31:49 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:31:50 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:31:50 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:31:50 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:31:50 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 13
2025-06-03 22:31:50 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:31:50 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:31:55 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:31:55 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:31:55 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:31:55 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:31:55 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 14
2025-06-03 22:31:56 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:31:56 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:31:57 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartphone
2025-06-03 22:31:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:02 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:32:02 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:02 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartphone
2025-06-03 22:32:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:19 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:32:19 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:19 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:20 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:32:20 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:20 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:26 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:32:26 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:26 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:32 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=televisao
2025-06-03 22:32:32 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:32 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:37 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=geladeira
2025-06-03 22:32:38 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:38 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:41 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:32:41 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:32:42 | INFO     | __main__:run_api:83 - Iniciando API em 0.0.0.0:8000
2025-06-03 22:32:42 | INFO     | api.main:startup_event:42 - Banco de dados inicializado
2025-06-03 22:33:11 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:33:11 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:33:11 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:33:11 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:33:12 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:33:12 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:33:12 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:33:12 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:33:12 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:33:12 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 15
2025-06-03 22:33:11 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:33:11 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:33:11 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:33:11 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:33:12 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:33:12 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:33:12 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:33:12 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:33:12 | INFO     | core.base_scraper:update_scraping_job:138 - Job 15 atualizado: completed
2025-06-03 22:33:12 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:33:12 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:33:12 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:33:12 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:33:12 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 16
2025-06-03 22:33:13 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:33:13 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:33:19 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:33:19 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:19 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:23 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:33:23 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:33:23 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:33:23 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:33:23 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 17
2025-06-03 22:33:24 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:33:24 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:33:25 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:33:25 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:25 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:39 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:33:39 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:39 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:40 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:33:40 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:40 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:48 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:33:48 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:48 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:50 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:33:50 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:50 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:56 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:33:56 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:56 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:56 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:33:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:02 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:34:02 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:02 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:04 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:34:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:08 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:34:08 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:08 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:10 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:34:10 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:10 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:10 | INFO     | core.base_scraper:update_scraping_job:138 - Job 16 atualizado: completed
2025-06-03 22:34:10 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:34:10 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:34:10 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:34:10 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:34:12 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:34:12 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:34:12 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:34:12 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:34:13 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:34:13 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:34:13 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:34:13 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:34:13 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:34:13 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 18
2025-06-03 22:34:14 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:34:14 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:34:14 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:34:14 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:34:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:34:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:34:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:34:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:34:15 | INFO     | core.base_scraper:update_scraping_job:138 - Job 18 atualizado: completed
2025-06-03 22:34:15 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:34:15 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:34:15 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:34:15 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:34:15 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 19
2025-06-03 22:34:16 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:34:16 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:34:44 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:34:44 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:34:50 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:34:50 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:50 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:34:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:35:05 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:05 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:35:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:25 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:35:25 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:25 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:32 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:35:32 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:32 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:37 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:35:38 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:38 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:38 | INFO     | core.base_scraper:update_scraping_job:138 - Job 19 atualizado: completed
2025-06-03 22:35:38 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 22:35:38 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:35:38 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:35:38 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:35:39 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:35:39 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:35:39 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:35:39 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:35:40 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:35:40 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:35:40 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:35:40 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:35:40 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:35:40 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 20
2025-06-03 22:35:41 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:35:41 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:35:41 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:35:41 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:35:42 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:35:42 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:35:42 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:35:42 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:35:42 | INFO     | core.base_scraper:update_scraping_job:138 - Job 20 atualizado: completed
2025-06-03 22:35:42 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:35:42 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:35:42 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:35:42 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:35:42 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 21
2025-06-03 22:35:43 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:35:43 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:35:50 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:35:50 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:50 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:57 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:35:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:36:02 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:36:02 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:36:02 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:36:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:36:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:36:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:36:20 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:36:20 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:36:20 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:36:31 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:36:30 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:36:30 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:36:59 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 22:36:59 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 22:37:07 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:37:07 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:07 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:07 | INFO     | core.base_scraper:update_scraping_job:138 - Job 21 atualizado: completed
2025-06-03 22:37:07 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 22:37:07 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:37:07 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:37:07 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:37:08 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:37:08 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:37:08 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:37:08 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:37:09 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:37:09 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:37:09 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:37:09 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:37:09 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:37:09 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 22
2025-06-03 22:37:10 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:37:10 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:37:10 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:37:10 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:37:11 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:37:11 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:37:11 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:37:11 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:37:11 | INFO     | core.base_scraper:update_scraping_job:138 - Job 22 atualizado: completed
2025-06-03 22:37:11 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:37:11 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:37:11 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:37:11 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:37:11 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 23
2025-06-03 22:37:12 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:37:12 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:37:24 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:37:24 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:24 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:28 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:37:28 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:28 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:34 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:37:34 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:34 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:45 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:37:45 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:45 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:52 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:37:52 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:52 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:37:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:38:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:38:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:38:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:38:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:38:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:38:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:38:14 | INFO     | core.base_scraper:update_scraping_job:138 - Job 23 atualizado: completed
2025-06-03 22:38:14 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:38:14 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:38:14 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:38:14 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:38:16 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:38:16 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:38:16 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:38:16 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:38:17 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:38:17 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:38:17 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:38:17 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:38:17 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:38:17 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 24
2025-06-03 22:38:18 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:38:18 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:38:18 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:38:18 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:38:19 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:38:19 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:38:19 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:38:19 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:38:19 | INFO     | core.base_scraper:update_scraping_job:138 - Job 24 atualizado: completed
2025-06-03 22:38:19 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:38:19 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:38:19 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:38:19 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:38:19 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 25
2025-06-03 22:38:20 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:38:20 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:38:48 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:38:48 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:38:54 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:38:54 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:38:54 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:38:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:38:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:38:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:39:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:39:05 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:39:05 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:39:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:39:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:39:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:39:40 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/televisao: Timeout 30000ms exceeded.
2025-06-03 22:39:40 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/televisao: Timeout 30000ms exceeded.
2025-06-03 22:39:40 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/geladeira: net::ERR_CONNECTION_CLOSED at https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:39:40 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/geladeira: net::ERR_CONNECTION_CLOSED at https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:40:09 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/micro-ondas: Timeout 30000ms exceeded.
2025-06-03 22:40:09 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/micro-ondas: Timeout 30000ms exceeded.
2025-06-03 22:40:09 | INFO     | core.base_scraper:update_scraping_job:138 - Job 25 atualizado: completed
2025-06-03 22:40:09 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 4 erros
2025-06-03 22:40:09 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:40:09 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:40:09 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:40:11 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:40:11 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:40:11 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:40:11 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:40:13 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:40:13 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:40:13 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:40:13 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:40:13 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:40:13 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 26
2025-06-03 22:40:14 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:40:14 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:40:14 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:40:14 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:40:14 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:40:14 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:40:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:40:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:40:15 | INFO     | core.base_scraper:update_scraping_job:138 - Job 26 atualizado: completed
2025-06-03 22:40:15 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:40:15 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:40:15 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:40:15 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:40:15 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 27
2025-06-03 22:40:16 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:40:16 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
