2025-06-03 22:24:52 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:24:52 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:24:53 | INFO     | __main__:run_api:83 - Iniciando API em 0.0.0.0:8000
2025-06-03 22:24:53 | INFO     | api.main:startup_event:42 - Banco de dados inicializado
2025-06-03 22:25:22 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:25:22 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:25:22 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:25:22 | INFO     | __main__:main:117 - <PERSON><PERSON><PERSON> inicializado com sucesso!
2025-06-03 22:25:23 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:25:23 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:25:23 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:25:23 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:25:23 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:25:23 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 1
2025-06-03 22:25:24 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:25:24 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:25:24 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:25:24 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:25:25 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:25:25 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:25:25 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:25:25 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:25:25 | INFO     | core.base_scraper:update_scraping_job:138 - Job 1 atualizado: completed
2025-06-03 22:25:25 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:25:25 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:25:25 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:25:25 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:25:25 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 2
2025-06-03 22:25:26 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:25:26 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:25:55 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:25:55 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:26:23 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=notebook: Timeout 30000ms exceeded.
2025-06-03 22:26:23 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=notebook: Timeout 30000ms exceeded.
2025-06-03 22:26:37 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:26:37 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:26:37 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:06 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 22:27:06 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 22:27:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:27:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:21 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=televisao
2025-06-03 22:27:21 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:21 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:28 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=geladeira
2025-06-03 22:27:28 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:28 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:34 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=micro-ondas
2025-06-03 22:27:34 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:34 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:34 | INFO     | core.base_scraper:update_scraping_job:138 - Job 2 atualizado: completed
2025-06-03 22:27:34 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:27:34 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:27:34 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:27:34 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:27:36 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:27:36 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:27:36 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:27:36 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:27:37 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:27:37 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:27:37 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:27:37 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:27:37 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:27:37 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 3
2025-06-03 22:27:37 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:27:37 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:27:38 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:27:38 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:27:38 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:27:38 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:27:39 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:27:39 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:27:39 | INFO     | core.base_scraper:update_scraping_job:138 - Job 3 atualizado: completed
2025-06-03 22:27:39 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:27:39 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:27:39 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:27:39 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:27:39 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 4
2025-06-03 22:27:39 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:27:39 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:27:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartphone
2025-06-03 22:27:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:52 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:27:52 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:52 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:27:57 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:27:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:27:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:06 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:28:06 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:28:06 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:13 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:28:13 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:28:13 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:23 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=televisao
2025-06-03 22:28:23 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:28:23 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:26 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=geladeira
2025-06-03 22:28:27 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:28:27 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:34 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:28:34 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:28:34 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:28:34 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:28:34 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:28:34 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 5
2025-06-03 22:28:34 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:28:34 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:28:35 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:28:35 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:28:35 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=micro-ondas
2025-06-03 22:28:35 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:28:35 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:28:35 | INFO     | core.base_scraper:update_scraping_job:138 - Job 4 atualizado: completed
2025-06-03 22:28:35 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:28:35 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:28:35 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:28:35 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:28:37 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:28:37 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:28:37 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:28:37 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:28:38 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:28:38 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:28:38 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:28:38 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:28:38 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:28:38 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 6
2025-06-03 22:28:38 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:28:38 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:28:39 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:28:39 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:28:39 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:28:39 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:28:40 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:28:40 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:28:40 | INFO     | core.base_scraper:update_scraping_job:138 - Job 6 atualizado: completed
2025-06-03 22:28:40 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:28:40 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:28:40 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:28:40 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:28:40 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 7
2025-06-03 22:28:40 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:28:40 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:29:09 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:29:09 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:29:15 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:29:15 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:29:15 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:29:34 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:29:34 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:29:34 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:29:36 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:29:36 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:29:36 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:29:36 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:29:36 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 8
2025-06-03 22:29:37 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:29:37 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:29:48 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:29:48 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:29:48 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:29:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:29:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:29:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:06 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:30:06 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=smartphone: Timeout 30000ms exceeded.
2025-06-03 22:30:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:30:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:20 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:30:20 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:20 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:26 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://www.mercadolivre.com.br/jm/search?as_word=televisao: Timeout 30000ms exceeded.
2025-06-03 22:30:26 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.mercadolivre.com.br/jm/search?as_word=televisao: Timeout 30000ms exceeded.
2025-06-03 22:30:29 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:30:29 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:29 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:32 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=geladeira
2025-06-03 22:30:32 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:32 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:35 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:30:35 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:35 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:40 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=televisao
2025-06-03 22:30:40 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:40 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:45 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=micro-ondas
2025-06-03 22:30:45 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:45 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:30:45 | INFO     | core.base_scraper:update_scraping_job:138 - Job 7 atualizado: completed
2025-06-03 22:30:45 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 2 erros
2025-06-03 22:30:45 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:30:45 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:30:45 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:30:47 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:30:47 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:30:47 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:30:47 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:30:48 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:30:48 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:30:48 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:30:48 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:30:48 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:30:48 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 9
2025-06-03 22:30:49 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:30:49 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:30:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:30:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:30:48 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:30:48 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:30:48 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:30:48 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:30:48 | INFO     | core.base_scraper:update_scraping_job:138 - Job 9 atualizado: completed
2025-06-03 22:30:48 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:30:48 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:30:48 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:30:48 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:30:48 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 10
2025-06-03 22:30:49 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:30:49 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:30:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartphone
2025-06-03 22:30:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:30:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:31:05 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:05 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:13 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:31:13 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:13 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:21 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:31:21 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:21 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:27 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:31:27 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:27 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:29 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:31:29 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:31:29 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:31:29 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:31:29 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 11
2025-06-03 22:31:30 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:31:30 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:31:32 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=televisao
2025-06-03 22:31:33 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:33 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:38 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=geladeira
2025-06-03 22:31:38 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:38 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:40 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartphone
2025-06-03 22:31:40 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:40 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=micro-ondas
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:31:46 | INFO     | core.base_scraper:update_scraping_job:138 - Job 10 atualizado: completed
2025-06-03 22:31:46 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:31:46 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:31:46 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:31:46 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:31:46 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:31:46 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:31:47 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:31:47 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:31:47 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:31:47 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:31:47 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:31:47 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:31:47 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:31:47 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 12
2025-06-03 22:31:48 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:31:48 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:31:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:31:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:31:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:31:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:31:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:31:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:31:49 | INFO     | core.base_scraper:update_scraping_job:138 - Job 12 atualizado: completed
2025-06-03 22:31:49 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:31:50 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:31:50 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:31:50 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:31:50 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 13
2025-06-03 22:31:50 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:31:50 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:31:55 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:31:55 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:31:55 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:31:55 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:31:55 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 14
2025-06-03 22:31:56 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:31:56 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:31:57 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartphone
2025-06-03 22:31:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:31:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:02 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:32:02 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:02 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartphone
2025-06-03 22:32:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=notebook
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:09 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=tablet
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:19 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:32:19 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:19 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:20 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=fone%20de%20ouvido
2025-06-03 22:32:20 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:20 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:26 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=smartwatch
2025-06-03 22:32:26 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:26 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:32 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=televisao
2025-06-03 22:32:32 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:32 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:37 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://www.mercadolivre.com.br/jm/search?as_word=geladeira
2025-06-03 22:32:38 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:32:38 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:32:41 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:32:41 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:32:42 | INFO     | __main__:run_api:83 - Iniciando API em 0.0.0.0:8000
2025-06-03 22:32:42 | INFO     | api.main:startup_event:42 - Banco de dados inicializado
2025-06-03 22:33:11 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:33:11 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:33:11 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:33:11 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:33:12 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:33:12 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:33:12 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:33:12 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:33:12 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:33:12 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 15
2025-06-03 22:33:11 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:33:11 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:33:11 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:33:11 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:33:12 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:33:12 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:33:12 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:33:12 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:33:12 | INFO     | core.base_scraper:update_scraping_job:138 - Job 15 atualizado: completed
2025-06-03 22:33:12 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:33:12 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:33:12 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:33:12 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:33:12 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 16
2025-06-03 22:33:13 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:33:13 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:33:19 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:33:19 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:19 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:23 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:33:23 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:33:23 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:33:23 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:33:23 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 17
2025-06-03 22:33:24 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:33:24 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:33:25 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:33:25 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:25 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:33 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:39 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:33:39 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:39 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:40 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:33:40 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:40 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:48 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:33:48 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:48 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:50 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:33:50 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:50 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:56 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:33:56 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:56 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:33:56 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:33:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:33:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:02 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:34:02 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:02 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:04 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:34:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:08 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:34:08 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:08 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:10 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:34:10 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:10 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:10 | INFO     | core.base_scraper:update_scraping_job:138 - Job 16 atualizado: completed
2025-06-03 22:34:10 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:34:10 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:34:10 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:34:10 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:34:12 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:34:12 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:34:12 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:34:12 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:34:13 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:34:13 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:34:13 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:34:13 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:34:13 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:34:13 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 18
2025-06-03 22:34:14 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:34:14 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:34:14 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:34:14 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:34:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:34:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:34:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:34:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:34:15 | INFO     | core.base_scraper:update_scraping_job:138 - Job 18 atualizado: completed
2025-06-03 22:34:15 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:34:15 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:34:15 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:34:15 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:34:15 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 19
2025-06-03 22:34:16 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:34:16 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:34:44 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:34:44 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:34:50 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:34:50 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:50 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:34:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:34:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:34:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:35:05 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:05 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:35:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:25 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:35:25 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:25 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:32 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:35:32 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:32 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:37 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:35:38 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:38 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:38 | INFO     | core.base_scraper:update_scraping_job:138 - Job 19 atualizado: completed
2025-06-03 22:35:38 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 22:35:38 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:35:38 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:35:38 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:35:39 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:35:39 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:35:39 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:35:39 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:35:40 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:35:40 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:35:40 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:35:40 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:35:40 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:35:40 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 20
2025-06-03 22:35:41 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:35:41 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:35:41 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:35:41 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:35:42 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:35:42 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:35:42 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:35:42 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:35:42 | INFO     | core.base_scraper:update_scraping_job:138 - Job 20 atualizado: completed
2025-06-03 22:35:42 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:35:42 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:35:42 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:35:42 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:35:42 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 21
2025-06-03 22:35:43 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:35:43 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:35:50 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:35:50 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:50 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:35:57 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:35:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:35:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:36:02 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:36:02 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:36:02 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:36:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:36:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:36:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:36:20 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:36:20 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:36:20 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:36:31 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:36:30 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:36:30 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:36:59 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 22:36:59 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 22:37:07 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:37:07 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:07 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:07 | INFO     | core.base_scraper:update_scraping_job:138 - Job 21 atualizado: completed
2025-06-03 22:37:07 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 22:37:07 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:37:07 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:37:07 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:37:08 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:37:08 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:37:08 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:37:08 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:37:09 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:37:09 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:37:09 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:37:09 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:37:09 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:37:09 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 22
2025-06-03 22:37:10 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:37:10 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:37:10 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:37:10 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:37:11 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:37:11 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:37:11 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:37:11 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:37:11 | INFO     | core.base_scraper:update_scraping_job:138 - Job 22 atualizado: completed
2025-06-03 22:37:11 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:37:11 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:37:11 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:37:11 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:37:11 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 23
2025-06-03 22:37:12 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:37:12 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:37:24 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:37:24 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:24 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:28 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:37:28 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:28 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:34 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:37:34 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:34 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:45 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:37:45 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:45 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:52 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:37:52 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:52 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:37:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:37:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:37:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:38:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:38:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:38:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:38:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:38:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:38:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:38:14 | INFO     | core.base_scraper:update_scraping_job:138 - Job 23 atualizado: completed
2025-06-03 22:38:14 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:38:14 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:38:14 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:38:14 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:38:16 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:38:16 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:38:16 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:38:16 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:38:17 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:38:17 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:38:17 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:38:17 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:38:17 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:38:17 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 24
2025-06-03 22:38:18 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:38:18 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:38:18 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:38:18 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:38:19 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:38:19 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:38:19 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:38:19 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:38:19 | INFO     | core.base_scraper:update_scraping_job:138 - Job 24 atualizado: completed
2025-06-03 22:38:19 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:38:19 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:38:19 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:38:19 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:38:19 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 25
2025-06-03 22:38:20 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:38:20 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:38:48 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:38:48 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:38:54 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:38:54 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:38:54 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:38:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:38:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:38:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:39:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:39:05 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:39:05 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:39:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:39:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:39:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:39:40 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/televisao: Timeout 30000ms exceeded.
2025-06-03 22:39:40 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/televisao: Timeout 30000ms exceeded.
2025-06-03 22:39:40 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/geladeira: net::ERR_CONNECTION_CLOSED at https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:39:40 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/geladeira: net::ERR_CONNECTION_CLOSED at https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:40:09 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/micro-ondas: Timeout 30000ms exceeded.
2025-06-03 22:40:09 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/micro-ondas: Timeout 30000ms exceeded.
2025-06-03 22:40:09 | INFO     | core.base_scraper:update_scraping_job:138 - Job 25 atualizado: completed
2025-06-03 22:40:09 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 4 erros
2025-06-03 22:40:09 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:40:09 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:40:09 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:40:11 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:40:11 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:40:11 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:40:11 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:40:13 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:40:13 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:40:13 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:40:13 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:40:13 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:40:13 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 26
2025-06-03 22:40:14 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:40:14 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:40:14 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:40:14 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:40:14 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:40:14 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:40:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:40:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:40:15 | INFO     | core.base_scraper:update_scraping_job:138 - Job 26 atualizado: completed
2025-06-03 22:40:15 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:40:15 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:40:15 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:40:15 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:40:15 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 27
2025-06-03 22:40:16 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:40:16 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:40:44 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:40:44 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:40:52 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:40:52 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:40:52 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:41:01 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:41:01 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:41:01 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:41:08 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:41:08 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:41:08 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:41:36 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartwatch: Timeout 30000ms exceeded.
2025-06-03 22:41:36 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartwatch: Timeout 30000ms exceeded.
2025-06-03 22:41:45 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:41:45 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:41:45 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:42:08 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:42:08 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:42:08 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:42:08 | ERROR    | __main__:main:134 - Scraper 'drogasil' não encontrado
2025-06-03 22:42:14 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 22:42:14 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 22:42:20 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:42:20 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:42:20 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:42:20 | INFO     | core.base_scraper:update_scraping_job:138 - Job 27 atualizado: completed
2025-06-03 22:42:20 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:42:20 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:42:20 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:42:20 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:42:22 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:42:22 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:42:22 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:42:22 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:42:23 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:42:23 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:42:23 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:42:23 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:42:23 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:42:23 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 28
2025-06-03 22:42:24 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:42:24 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:42:24 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:42:24 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:42:25 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:42:25 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:42:25 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:42:25 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:42:25 | INFO     | core.base_scraper:update_scraping_job:138 - Job 28 atualizado: completed
2025-06-03 22:42:25 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:42:25 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:42:25 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:42:25 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:42:25 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 29
2025-06-03 22:42:26 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:42:26 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:42:54 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:42:54 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:43:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:43:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:43:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:43:10 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:43:10 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:43:10 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:43:23 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:43:23 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:43:23 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:43:29 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:43:30 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:43:30 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:43:35 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:43:35 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:43:35 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:43:40 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:43:40 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:43:40 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:43:49 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:43:49 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:43:49 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:43:49 | INFO     | core.base_scraper:update_scraping_job:138 - Job 29 atualizado: completed
2025-06-03 22:43:49 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 22:43:49 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:43:49 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:43:49 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:43:51 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:43:51 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:43:51 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:43:51 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:43:52 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:43:52 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:43:52 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:43:52 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:43:52 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:43:52 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 30
2025-06-03 22:43:53 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:43:53 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:43:53 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:43:53 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:43:54 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:43:54 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:43:54 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:43:54 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:43:54 | INFO     | core.base_scraper:update_scraping_job:138 - Job 30 atualizado: completed
2025-06-03 22:43:54 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:43:54 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:43:54 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:43:54 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:43:54 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 31
2025-06-03 22:43:55 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:43:55 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:44:02 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:44:02 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:44:02 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:44:07 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:44:07 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:44:07 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:44:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:44:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:44:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:44:23 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:44:23 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:44:23 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:44:30 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:44:30 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:44:30 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:44:36 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:44:36 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:44:36 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:44:42 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:44:42 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:44:42 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:44:47 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:44:47 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:44:47 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:44:47 | INFO     | core.base_scraper:update_scraping_job:138 - Job 31 atualizado: completed
2025-06-03 22:44:47 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:44:47 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:44:47 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:44:47 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:44:49 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:44:49 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:44:49 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:44:49 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:44:50 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:44:50 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:44:50 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:44:50 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:44:50 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:44:50 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 32
2025-06-03 22:44:50 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:44:50 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:44:51 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:44:51 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:44:51 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:44:51 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:44:52 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:44:52 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:44:52 | INFO     | core.base_scraper:update_scraping_job:138 - Job 32 atualizado: completed
2025-06-03 22:44:52 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:44:52 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:44:52 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:44:52 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:44:52 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 33
2025-06-03 22:44:52 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:44:52 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:45:04 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:45:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:45:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:45:10 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:45:10 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:45:10 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:45:16 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:45:16 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:45:16 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:45:22 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:45:22 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:45:22 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:45:29 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:45:29 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:45:29 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:45:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:45:47 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:45:47 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:45:49 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:45:49 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:45:49 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:45:49 | ERROR    | __main__:main:134 - Scraper 'drogasil' não encontrado
2025-06-03 22:45:54 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:45:54 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:45:54 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:46:01 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:46:01 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:46:01 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:46:01 | INFO     | core.base_scraper:update_scraping_job:138 - Job 33 atualizado: completed
2025-06-03 22:46:01 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:46:01 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:46:01 | INFO     | __main__:run_scraper:54 - Scraper mercadolivre concluído
2025-06-03 22:46:01 | INFO     | __main__:run_all_scrapers:66 - Execução de todos os scrapers concluída
2025-06-03 22:46:02 | INFO     | __main__:main:115 - Inicializando sistema...
2025-06-03 22:46:02 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:46:02 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:46:02 | INFO     | __main__:main:117 - Sistema inicializado com sucesso!
2025-06-03 22:46:03 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:46:03 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:46:03 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:46:03 | INFO     | __main__:run_all_scrapers:58 - Iniciando execução de todos os scrapers
2025-06-03 22:46:03 | INFO     | __main__:run_scraper:52 - Iniciando scraper: example
2025-06-03 22:46:03 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 34
2025-06-03 22:46:04 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:46:04 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:46:04 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:46:04 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:46:04 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:46:04 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:46:05 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:46:05 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:46:05 | INFO     | core.base_scraper:update_scraping_job:138 - Job 34 atualizado: completed
2025-06-03 22:46:05 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:46:05 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:46:05 | INFO     | __main__:run_scraper:54 - Scraper example concluído
2025-06-03 22:46:05 | INFO     | __main__:run_scraper:52 - Iniciando scraper: mercadolivre
2025-06-03 22:46:05 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 35
2025-06-03 22:46:06 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:46:06 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:46:19 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:46:19 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:46:19 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:46:25 | INFO     | __main__:init_database:71 - Inicializando banco de dados...
2025-06-03 22:46:25 | INFO     | __main__:init_database:73 - Banco de dados inicializado com sucesso
2025-06-03 22:46:25 | INFO     | __main__:register_scrapers:38 - Scrapers registrados: ['example', 'mercadolivre']
2025-06-03 22:46:25 | ERROR    | __main__:main:134 - Scraper 'drogasil' não encontrado
2025-06-03 22:46:31 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:46:31 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:46:31 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:46:41 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:46:41 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:46:41 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:46:49 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:46:49 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:46:49 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:46:56 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:46:56 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:46:56 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:48:42 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 22:48:43 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 22:48:43 | INFO     | __main__:run_api:85 - Iniciando API em 0.0.0.0:8000
2025-06-03 22:48:43 | INFO     | api.main:startup_event:42 - Banco de dados inicializado
2025-06-03 22:49:12 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 22:49:12 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 22:49:12 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 22:49:12 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 22:49:13 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 22:49:13 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 22:49:13 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 22:49:13 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 22:49:13 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 22:49:13 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 36
2025-06-03 22:49:14 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:49:14 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:49:14 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:49:14 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:49:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:49:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:49:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:49:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:49:15 | INFO     | core.base_scraper:update_scraping_job:138 - Job 36 atualizado: completed
2025-06-03 22:49:15 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:49:15 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:49:15 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 22:49:15 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 22:49:15 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 37
2025-06-03 22:49:16 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:49:16 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:49:36 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 22:49:45 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:49:45 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:49:46 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 22:49:46 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 22:49:46 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 22:49:46 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 22:49:46 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 38
2025-06-03 22:49:46 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:49:46 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:49:50 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891000100103
2025-06-03 22:49:53 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:49:53 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:49:53 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:50:04 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:50:08 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891000053508
2025-06-03 22:50:21 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:50:22 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 22:50:22 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 22:50:25 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891000244234
2025-06-03 22:50:40 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:50:44 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891991010016
2025-06-03 22:50:50 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 22:50:50 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 22:50:56 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:50:56 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:50:56 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:50:57 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:51:01 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891106001533
2025-06-03 22:51:16 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:51:18 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891106001540
2025-06-03 22:51:24 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/televisao: Timeout 30000ms exceeded.
2025-06-03 22:51:24 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/televisao: Timeout 30000ms exceeded.
2025-06-03 22:51:33 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:51:33 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:51:33 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:51:33 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:51:37 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891106001557
2025-06-03 22:51:38 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:51:38 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:51:38 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:51:38 | INFO     | core.base_scraper:update_scraping_job:138 - Job 37 atualizado: completed
2025-06-03 22:51:38 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 4 erros
2025-06-03 22:51:38 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:51:38 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 22:51:38 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 22:51:38 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 39
2025-06-03 22:51:39 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:51:39 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:51:43 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891000100103
2025-06-03 22:51:50 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:51:54 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891106001564
2025-06-03 22:51:56 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:51:59 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:52:00 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891000053508
2025-06-03 22:52:03 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891000100103
2025-06-03 22:52:09 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:52:09 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:52:09 | INFO     | core.base_scraper:update_scraping_job:138 - Job 38 atualizado: completed
2025-06-03 22:52:09 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:52:09 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:52:09 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 22:52:13 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:52:17 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891000244234
2025-06-03 22:52:32 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:52:36 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891991010016
2025-06-03 22:52:50 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:52:52 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:52:53 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891106001533
2025-06-03 22:53:03 | INFO     | services.drogasil_scraper:navigate_to_page:392 - Navegou para: https://www.drogasil.com.br/search?w=7891000100103
2025-06-03 22:53:08 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:53:08 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:53:11 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891106001540
2025-06-03 22:53:26 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:53:30 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891106001557
2025-06-03 22:53:43 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:53:47 | INFO     | services.drogasil_scraper:navigate_to_page:366 - Navegou para: https://www.drogasil.com.br/search?w=7891106001564
2025-06-03 22:54:02 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:54:02 | INFO     | core.base_scraper:update_scraping_job:138 - Job 39 atualizado: completed
2025-06-03 22:54:02 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:54:02 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:54:02 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 22:54:02 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 22:54:04 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 22:54:04 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 22:54:04 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 22:54:04 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 22:54:05 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 22:54:05 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 22:54:05 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 22:54:05 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 22:54:05 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 22:54:05 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 40
2025-06-03 22:54:06 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:54:06 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:54:06 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:54:06 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:54:07 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:54:07 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:54:07 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:54:07 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:54:07 | INFO     | core.base_scraper:update_scraping_job:138 - Job 40 atualizado: completed
2025-06-03 22:54:07 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:54:07 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:54:07 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 22:54:07 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 22:54:07 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 41
2025-06-03 22:54:06 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:54:06 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:54:31 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:54:35 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:54:35 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 22:54:39 | INFO     | services.drogasil_scraper:navigate_to_page:392 - Navegou para: https://www.drogasil.com.br/search?w=7891000100103
2025-06-03 22:54:45 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:54:45 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:54:45 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:54:52 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:54:52 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:54:52 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:54:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:54:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:54:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:55:04 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:55:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:55:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:55:10 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:55:10 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:55:10 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:55:23 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:55:24 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 22:55:24 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:55:24 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:55:33 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:55:33 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:55:33 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:55:33 | INFO     | core.base_scraper:update_scraping_job:138 - Job 41 atualizado: completed
2025-06-03 22:55:33 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 22:55:33 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:55:33 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 22:55:33 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 22:55:33 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 42
2025-06-03 22:55:32 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:55:32 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:55:44 | INFO     | services.drogasil_scraper:navigate_to_page:392 - Navegou para: https://www.drogasil.com.br/search?w=7891000100103
2025-06-03 22:55:55 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:55:59 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:56:04 | INFO     | services.drogasil_scraper:navigate_to_page:385 - Navegou para: https://www.drogasil.com.br/search?w=7896658003004
2025-06-03 22:56:05 | INFO     | services.drogasil_scraper:navigate_to_page:392 - Navegou para: https://www.drogasil.com.br/search?w=7891000053508
2025-06-03 22:56:14 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:56:14 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:56:20 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:56:25 | INFO     | services.drogasil_scraper:navigate_to_page:385 - Navegou para: https://www.drogasil.com.br/search?w=7896658003011
2025-06-03 22:56:27 | INFO     | services.drogasil_scraper:navigate_to_page:392 - Navegou para: https://www.drogasil.com.br/search?w=7891000244234
2025-06-03 22:56:33 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:56:34 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:56:41 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:56:44 | INFO     | services.drogasil_scraper:navigate_to_page:385 - Navegou para: https://www.drogasil.com.br/search?w=7896658003028
2025-06-03 22:56:48 | INFO     | services.drogasil_scraper:navigate_to_page:392 - Navegou para: https://www.drogasil.com.br/search?w=7891991010016
2025-06-03 22:56:54 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:56:55 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:57:02 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:57:03 | INFO     | services.drogasil_scraper:navigate_to_page:385 - Navegou para: https://www.drogasil.com.br/search?w=7891317000015
2025-06-03 22:57:10 | INFO     | services.drogasil_scraper:navigate_to_page:392 - Navegou para: https://www.drogasil.com.br/search?w=7891106001533
2025-06-03 22:57:14 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:57:14 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:57:25 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:57:25 | INFO     | services.drogasil_scraper:navigate_to_page:385 - Navegou para: https://www.drogasil.com.br/search?w=7891317000022
2025-06-03 22:57:30 | INFO     | services.drogasil_scraper:navigate_to_page:392 - Navegou para: https://www.drogasil.com.br/search?w=7891106001540
2025-06-03 22:57:34 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:57:45 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:57:52 | INFO     | services.drogasil_scraper:navigate_to_page:392 - Navegou para: https://www.drogasil.com.br/search?w=7891106001557
2025-06-03 22:58:06 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:58:06 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:58:14 | INFO     | services.drogasil_scraper:navigate_to_page:392 - Navegou para: https://www.drogasil.com.br/search?w=7891106001564
2025-06-03 22:58:18 | INFO     | services.drogasil_scraper:navigate_to_page:385 - Navegou para: https://www.drogasil.com.br/search?w=paracetamol
2025-06-03 22:58:28 | ERROR    | services.drogasil_scraper:scrape:86 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 22:58:28 | INFO     | core.base_scraper:update_scraping_job:138 - Job 42 atualizado: completed
2025-06-03 22:58:28 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 22:58:28 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:58:28 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 22:58:28 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 22:58:28 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 22:58:30 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 22:58:30 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 22:58:30 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 22:58:30 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 22:58:32 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 22:58:32 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 22:58:32 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 22:58:32 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 22:58:32 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 22:58:32 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 43
2025-06-03 22:58:33 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 22:58:33 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 22:58:33 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:58:33 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 22:58:34 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:58:34 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 22:58:34 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:58:34 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 22:58:34 | INFO     | core.base_scraper:update_scraping_job:138 - Job 43 atualizado: completed
2025-06-03 22:58:34 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 22:58:35 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 22:58:35 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 22:58:35 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 22:58:35 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 44
2025-06-03 22:58:35 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 22:58:35 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 22:58:47 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 22:58:47 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:58:47 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:58:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 22:58:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:58:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:59:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 22:59:05 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:59:05 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:59:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 22:59:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:59:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:59:18 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 22:59:18 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:59:18 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:59:22 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 22:59:22 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:59:22 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:59:51 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 22:59:51 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 22:59:56 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 22:59:56 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 22:59:56 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 22:59:56 | INFO     | core.base_scraper:update_scraping_job:138 - Job 44 atualizado: completed
2025-06-03 22:59:56 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 22:59:56 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 22:59:56 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 22:59:56 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 22:59:56 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 45
2025-06-03 22:59:57 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 22:59:57 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:00:26 | ERROR    | services.drogasil_scraper:navigate_to_page:388 - Erro ao navegar para https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:00:26 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:00:26 | INFO     | core.base_scraper:update_scraping_job:138 - Job 45 atualizado: completed
2025-06-03 23:00:26 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:00:26 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:00:26 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:00:26 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:03:01 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:03:01 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:03:01 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:03:01 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:03:02 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:03:02 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:03:02 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:03:02 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:03:02 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:03:02 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 46
2025-06-03 23:03:03 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:03:03 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:03:04 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:03:04 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:03:04 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:03:04 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:03:04 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:03:04 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:03:04 | INFO     | core.base_scraper:update_scraping_job:138 - Job 46 atualizado: completed
2025-06-03 23:03:04 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:03:05 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:03:05 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:03:05 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:03:05 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 47
2025-06-03 23:03:05 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:03:05 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:03:10 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:03:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:03:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:03:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:03:14 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:03:14 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:03:14 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:03:14 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:03:14 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 48
2025-06-03 23:03:15 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:03:15 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:03:17 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:03:17 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:03:17 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:03:17 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:03:24 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:03:24 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:03:24 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:03:31 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:03:31 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:03:31 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:03:32 | ERROR    | services.drogasil_scraper:scrape:28 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 23:03:32 | INFO     | core.base_scraper:update_scraping_job:138 - Job 48 atualizado: completed
2025-06-03 23:03:32 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:03:32 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:03:32 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:03:38 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:03:38 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:03:38 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:03:42 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:03:45 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:03:49 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:03:49 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:03:49 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:03:55 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:03:55 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:03:55 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:03:55 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:04:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:04:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:04:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:04:03 | INFO     | core.base_scraper:update_scraping_job:138 - Job 47 atualizado: completed
2025-06-03 23:04:03 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:04:03 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:04:03 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:04:03 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:04:03 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 49
2025-06-03 23:04:04 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:04:04 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:04:06 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:04:20 | ERROR    | services.drogasil_scraper:scrape:28 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 23:04:20 | INFO     | core.base_scraper:update_scraping_job:138 - Job 49 atualizado: completed
2025-06-03 23:04:20 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:04:20 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:04:20 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:04:20 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:04:22 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:04:22 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:04:22 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:04:22 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:04:23 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:04:23 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:04:23 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:04:23 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:04:23 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:04:23 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 50
2025-06-03 23:04:24 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:04:24 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:04:24 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:04:24 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:04:25 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:04:25 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:04:25 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:04:25 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:04:25 | INFO     | core.base_scraper:update_scraping_job:138 - Job 50 atualizado: completed
2025-06-03 23:04:25 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:04:26 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:04:26 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:04:26 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:04:26 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 51
2025-06-03 23:04:26 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:04:26 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:04:41 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:04:55 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:04:55 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:05:10 | ERROR    | services.drogasil_scraper:navigate_to_page:101 - Erro ao navegar para https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:05:10 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:05:23 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/notebook: Timeout 30000ms exceeded.
2025-06-03 23:05:23 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/notebook: Timeout 30000ms exceeded.
2025-06-03 23:05:30 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:05:30 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:05:30 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:05:42 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:05:42 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:05:42 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:05:49 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:05:49 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:05:49 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:05:54 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:05:54 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:05:54 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:06:00 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:06:00 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:06:00 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:06:15 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:06:15 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:06:15 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:06:15 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:06:15 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 52
2025-06-03 23:06:16 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:06:16 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:06:18 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:06:18 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:06:18 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:06:18 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:06:18 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:06:18 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:06:18 | INFO     | core.base_scraper:update_scraping_job:138 - Job 52 atualizado: completed
2025-06-03 23:06:18 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:06:18 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:06:18 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:06:29 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/micro-ondas: Timeout 30000ms exceeded.
2025-06-03 23:06:29 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/micro-ondas: Timeout 30000ms exceeded.
2025-06-03 23:06:29 | INFO     | core.base_scraper:update_scraping_job:138 - Job 51 atualizado: completed
2025-06-03 23:06:29 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:06:29 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:06:29 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:06:29 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:06:29 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 53
2025-06-03 23:06:28 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:06:28 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:06:31 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:06:46 | ERROR    | services.drogasil_scraper:scrape:28 - Erro ao fazer scraping da página: Timeout 15000ms exceeded.
2025-06-03 23:06:46 | INFO     | core.base_scraper:update_scraping_job:138 - Job 53 atualizado: completed
2025-06-03 23:06:46 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:06:46 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:06:46 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:06:46 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:06:48 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:06:48 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:06:48 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:06:48 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:06:49 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:06:49 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:06:49 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:06:49 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:06:49 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:06:49 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 54
2025-06-03 23:06:50 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:06:50 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:06:50 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:06:50 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:06:51 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:06:51 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:06:51 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:06:51 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:06:51 | INFO     | core.base_scraper:update_scraping_job:138 - Job 54 atualizado: completed
2025-06-03 23:06:51 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:06:51 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:06:51 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:06:51 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:06:51 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 55
2025-06-03 23:06:52 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:06:52 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:06:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:06:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:06:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:07:04 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:07:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:07:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:07:13 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:07:13 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:07:13 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:07:21 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:07:21 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:07:21 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:07:28 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:07:28 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:07:28 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:07:56 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/televisao: Timeout 30000ms exceeded.
2025-06-03 23:07:56 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/televisao: Timeout 30000ms exceeded.
2025-06-03 23:08:02 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:08:02 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:08:02 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:08:10 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:08:10 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:08:10 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:08:10 | INFO     | core.base_scraper:update_scraping_job:138 - Job 55 atualizado: completed
2025-06-03 23:08:10 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:08:10 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:08:10 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:08:10 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:08:10 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 56
2025-06-03 23:08:11 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:08:11 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:08:13 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:08:13 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:08:13 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:08:13 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:08:13 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:08:13 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:08:13 | INFO     | core.base_scraper:update_scraping_job:138 - Job 56 atualizado: completed
2025-06-03 23:08:13 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:08:13 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:08:13 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:08:13 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:08:15 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:08:15 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:08:15 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:08:15 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:08:16 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:08:16 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:08:16 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:08:16 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:08:16 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:08:16 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 57
2025-06-03 23:08:17 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:08:17 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:08:17 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:08:17 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:08:18 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:08:18 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:08:18 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:08:18 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:08:18 | INFO     | core.base_scraper:update_scraping_job:138 - Job 57 atualizado: completed
2025-06-03 23:08:18 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:08:18 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:08:18 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:08:18 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:08:18 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 58
2025-06-03 23:08:19 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:08:19 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:08:47 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:08:47 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:08:52 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:08:52 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:08:52 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:08:59 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:08:59 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:08:59 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:09:06 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:09:06 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:09:06 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:09:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:09:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:09:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:09:20 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:09:20 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:09:20 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:09:24 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:09:24 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:09:24 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:09:30 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:09:30 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:09:30 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:09:30 | INFO     | core.base_scraper:update_scraping_job:138 - Job 58 atualizado: completed
2025-06-03 23:09:30 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:09:30 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:09:30 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:09:30 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:09:30 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 59
2025-06-03 23:09:31 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:09:31 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:09:33 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:09:33 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:09:33 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:09:33 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:09:33 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:09:33 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:09:33 | INFO     | core.base_scraper:update_scraping_job:138 - Job 59 atualizado: completed
2025-06-03 23:09:33 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:09:33 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:09:33 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:09:33 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:09:35 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:09:35 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:09:35 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:09:35 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:09:36 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:09:36 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:09:36 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:09:36 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:09:36 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:09:36 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 60
2025-06-03 23:09:37 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:09:37 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:09:37 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:09:37 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:09:38 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:09:38 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:09:38 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:09:38 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:09:38 | INFO     | core.base_scraper:update_scraping_job:138 - Job 60 atualizado: completed
2025-06-03 23:09:38 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:09:38 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:09:38 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:09:38 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:09:38 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 61
2025-06-03 23:09:39 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:09:39 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:09:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:09:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:09:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:09:50 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:09:50 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:09:50 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:10:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:10:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:10:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:10:11 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:10:11 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:10:11 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:10:17 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:10:17 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:10:17 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:10:24 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:10:24 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:10:24 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:10:30 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:10:30 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:10:30 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:10:36 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:10:36 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:10:36 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:10:36 | INFO     | core.base_scraper:update_scraping_job:138 - Job 61 atualizado: completed
2025-06-03 23:10:36 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:10:36 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:10:36 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:10:36 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:10:36 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 62
2025-06-03 23:10:37 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:10:37 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:10:39 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:10:39 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:10:39 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:10:39 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:10:39 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:10:39 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:10:39 | INFO     | core.base_scraper:update_scraping_job:138 - Job 62 atualizado: completed
2025-06-03 23:10:39 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:10:39 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:10:39 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:10:39 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:10:41 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:10:41 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:10:41 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:10:41 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:10:42 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:10:42 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:10:42 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:10:42 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:10:42 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:10:42 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 63
2025-06-03 23:10:43 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:10:43 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:10:43 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:10:43 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:10:44 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:10:44 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:10:44 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:10:44 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:10:44 | INFO     | core.base_scraper:update_scraping_job:138 - Job 63 atualizado: completed
2025-06-03 23:10:44 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:10:44 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:10:44 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:10:44 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:10:44 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 64
2025-06-03 23:10:45 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:10:45 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:11:14 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:11:14 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:11:20 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:11:20 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:11:20 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:11:26 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:11:26 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:11:26 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:11:54 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 23:11:54 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 23:12:04 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:12:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:12:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:12:09 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:12:09 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:12:09 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:12:21 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:12:21 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:12:21 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:12:27 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:12:27 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:12:27 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:12:27 | INFO     | core.base_scraper:update_scraping_job:138 - Job 64 atualizado: completed
2025-06-03 23:12:27 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 2 erros
2025-06-03 23:12:27 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:12:27 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:12:27 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:12:27 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 65
2025-06-03 23:12:28 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:12:28 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:12:30 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:12:30 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:12:30 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:12:30 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:12:30 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:12:30 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:12:30 | INFO     | core.base_scraper:update_scraping_job:138 - Job 65 atualizado: completed
2025-06-03 23:12:30 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:12:30 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:12:30 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:12:30 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:12:32 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:12:32 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:12:32 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:12:32 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:12:34 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:12:34 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:12:34 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:12:34 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:12:34 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:12:34 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 66
2025-06-03 23:12:34 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:12:34 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:12:35 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:12:35 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:12:35 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:12:35 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:12:36 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:12:36 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:12:36 | INFO     | core.base_scraper:update_scraping_job:138 - Job 66 atualizado: completed
2025-06-03 23:12:36 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:12:36 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:12:36 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:12:36 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:12:36 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 67
2025-06-03 23:12:36 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:12:36 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:13:05 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:13:05 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:13:11 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:13:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:13:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:13:21 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:13:22 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:13:22 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:13:28 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:13:28 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:13:28 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:13:34 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:13:34 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:13:34 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:13:41 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:13:41 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:13:41 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:13:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:13:47 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:13:47 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:13:53 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:13:53 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:13:53 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:13:53 | INFO     | core.base_scraper:update_scraping_job:138 - Job 67 atualizado: completed
2025-06-03 23:13:53 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:13:53 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:13:53 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:13:53 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:13:53 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 68
2025-06-03 23:13:54 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:13:54 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:13:56 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:13:56 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:13:56 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:13:56 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:13:56 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:13:56 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:13:56 | INFO     | core.base_scraper:update_scraping_job:138 - Job 68 atualizado: completed
2025-06-03 23:13:56 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:13:56 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:13:56 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:13:56 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:13:58 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:13:58 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:13:58 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:13:58 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:13:59 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:13:59 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:13:59 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:13:59 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:13:59 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:14:00 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 69
2025-06-03 23:14:00 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:14:00 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:14:01 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:14:01 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:14:01 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:14:01 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:14:02 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:14:02 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:14:02 | INFO     | core.base_scraper:update_scraping_job:138 - Job 69 atualizado: completed
2025-06-03 23:14:02 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:14:02 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:14:02 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:14:02 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:14:02 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 70
2025-06-03 23:14:02 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:14:02 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:14:31 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:14:31 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:14:42 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:14:42 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:14:42 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:14:48 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:14:48 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:14:48 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:14:55 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:14:55 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:14:55 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:15:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:15:06 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:15:06 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:15:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:15:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:15:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:15:41 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 23:15:41 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 23:15:48 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:15:48 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:15:48 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:15:48 | INFO     | core.base_scraper:update_scraping_job:138 - Job 70 atualizado: completed
2025-06-03 23:15:48 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 2 erros
2025-06-03 23:15:48 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:15:48 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:15:48 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:15:48 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 71
2025-06-03 23:15:49 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:15:49 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:15:51 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:15:51 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:15:51 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:15:51 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:15:51 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:15:51 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:15:51 | INFO     | core.base_scraper:update_scraping_job:138 - Job 71 atualizado: completed
2025-06-03 23:15:51 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:15:51 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:15:51 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:15:51 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:15:53 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:15:53 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:15:53 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:15:53 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:15:55 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:15:55 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:15:55 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:15:55 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:15:55 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:15:55 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 72
2025-06-03 23:15:55 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:15:55 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:15:56 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:15:56 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:15:56 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:15:56 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:15:57 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:15:57 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:15:57 | INFO     | core.base_scraper:update_scraping_job:138 - Job 72 atualizado: completed
2025-06-03 23:15:57 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:15:57 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:15:57 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:15:57 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:15:57 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 73
2025-06-03 23:15:57 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:15:57 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:16:06 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:16:06 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:16:06 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:16:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:16:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:16:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:16:41 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 23:16:41 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 23:17:09 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 23:17:09 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 23:17:16 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:17:16 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:17:16 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:17:29 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:17:29 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:17:29 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:17:35 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:17:35 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:17:35 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:17:41 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:17:41 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:17:41 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:17:41 | INFO     | core.base_scraper:update_scraping_job:138 - Job 73 atualizado: completed
2025-06-03 23:17:41 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 2 erros
2025-06-03 23:17:41 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:17:41 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:17:41 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:17:41 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 74
2025-06-03 23:17:42 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:17:42 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:17:44 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:17:44 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:17:44 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:17:44 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:17:44 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:17:44 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:17:44 | INFO     | core.base_scraper:update_scraping_job:138 - Job 74 atualizado: completed
2025-06-03 23:17:44 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:17:44 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:17:44 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:17:44 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:17:47 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:17:47 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:17:47 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:17:47 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:17:48 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:17:48 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:17:48 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:17:48 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:17:48 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:17:48 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 75
2025-06-03 23:17:49 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:17:49 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:17:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:17:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:17:50 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:17:50 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:17:50 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:17:50 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:17:50 | INFO     | core.base_scraper:update_scraping_job:138 - Job 75 atualizado: completed
2025-06-03 23:17:50 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:17:50 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:17:50 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:17:50 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:17:50 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 76
2025-06-03 23:17:51 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:17:51 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:17:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:17:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:17:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:18:04 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:18:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:18:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:18:33 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 23:18:33 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 23:18:40 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:18:40 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:18:40 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:18:49 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:18:49 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:18:49 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:18:57 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:18:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:18:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:19:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:19:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:19:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:19:09 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:19:09 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:19:09 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:19:09 | INFO     | core.base_scraper:update_scraping_job:138 - Job 76 atualizado: completed
2025-06-03 23:19:09 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:19:09 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:19:09 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:19:09 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:19:09 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 77
2025-06-03 23:19:10 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:19:10 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:19:12 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:19:12 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:19:12 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:19:12 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:19:12 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:19:12 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:19:12 | INFO     | core.base_scraper:update_scraping_job:138 - Job 77 atualizado: completed
2025-06-03 23:19:12 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:19:12 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:19:12 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:19:12 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:19:14 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:19:14 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:19:14 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:19:14 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:19:16 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:19:16 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:19:16 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:19:16 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:19:16 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:19:16 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 78
2025-06-03 23:19:16 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:19:16 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:19:17 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:19:17 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:19:17 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:19:17 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:19:18 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:19:18 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:19:18 | INFO     | core.base_scraper:update_scraping_job:138 - Job 78 atualizado: completed
2025-06-03 23:19:18 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:19:18 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:19:18 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:19:18 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:19:18 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 79
2025-06-03 23:19:18 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:19:18 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:19:23 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:19:23 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:19:23 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:19:29 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:19:29 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:19:29 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:19:35 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:19:35 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:19:35 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:19:43 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:19:43 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:19:43 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:19:47 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:19:47 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:19:47 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:19:53 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:19:53 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:19:53 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:19:59 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:19:59 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:19:59 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:20:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:20:05 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:20:05 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:20:05 | INFO     | core.base_scraper:update_scraping_job:138 - Job 79 atualizado: completed
2025-06-03 23:20:05 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:20:05 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:20:05 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:20:05 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:20:05 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 80
2025-06-03 23:20:06 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:20:06 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:20:08 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:20:08 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:20:08 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:20:08 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:20:08 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:20:08 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:20:08 | INFO     | core.base_scraper:update_scraping_job:138 - Job 80 atualizado: completed
2025-06-03 23:20:08 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:20:08 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:20:08 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:20:08 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:20:10 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:20:10 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:20:10 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:20:10 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:20:11 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:20:11 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:20:11 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:20:11 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:20:11 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:20:11 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 81
2025-06-03 23:20:12 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:20:12 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:20:12 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:20:12 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:20:13 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:20:13 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:20:13 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:20:13 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:20:13 | INFO     | core.base_scraper:update_scraping_job:138 - Job 81 atualizado: completed
2025-06-03 23:20:13 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:20:13 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:20:13 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:20:13 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:20:13 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 82
2025-06-03 23:20:14 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:20:14 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:20:22 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:20:22 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:20:22 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:20:41 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:20:41 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:20:41 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:20:47 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:20:47 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:20:47 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:20:53 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:20:53 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:20:53 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:20:59 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:20:59 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:20:59 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:21:06 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:21:06 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:21:06 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:21:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:21:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:21:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:21:18 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:21:18 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:21:18 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:21:18 | INFO     | core.base_scraper:update_scraping_job:138 - Job 82 atualizado: completed
2025-06-03 23:21:18 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:21:19 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:21:19 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:21:19 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:21:19 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 83
2025-06-03 23:21:19 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:21:19 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:21:21 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:21:21 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:21:21 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:21:21 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:21:21 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:21:21 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:21:21 | INFO     | core.base_scraper:update_scraping_job:138 - Job 83 atualizado: completed
2025-06-03 23:21:21 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:21:22 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:21:22 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:21:22 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:21:24 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:21:24 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:21:24 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:21:24 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:21:25 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:21:25 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:21:25 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:21:25 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:21:25 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:21:25 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 84
2025-06-03 23:21:26 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:21:26 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:21:26 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:21:26 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:21:27 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:21:27 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:21:27 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:21:27 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:21:27 | INFO     | core.base_scraper:update_scraping_job:138 - Job 84 atualizado: completed
2025-06-03 23:21:27 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:21:27 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:21:27 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:21:27 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:21:27 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 85
2025-06-03 23:21:28 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:21:28 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:21:56 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:21:56 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:22:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:22:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:22:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:22:31 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 23:22:31 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 23:22:37 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:22:38 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:22:38 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:22:44 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:22:44 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:22:44 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:22:49 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:22:49 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:22:49 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:22:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:22:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:22:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:23:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:23:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:23:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:23:03 | INFO     | core.base_scraper:update_scraping_job:138 - Job 85 atualizado: completed
2025-06-03 23:23:03 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 2 erros
2025-06-03 23:23:04 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:23:04 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:23:04 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:23:04 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 86
2025-06-03 23:23:04 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:23:04 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:23:05 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:23:05 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:23:05 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:23:05 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:23:05 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 87
2025-06-03 23:23:06 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:23:06 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:23:07 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:23:07 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:23:07 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:23:07 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:23:07 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:23:07 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:23:07 | INFO     | core.base_scraper:update_scraping_job:138 - Job 86 atualizado: completed
2025-06-03 23:23:07 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:23:07 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:23:07 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:23:07 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:23:07 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:23:07 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:23:07 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:23:07 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:23:08 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:23:08 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:23:08 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:23:08 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:23:08 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:23:08 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 88
2025-06-03 23:23:09 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:23:09 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:23:09 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:23:09 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:23:10 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:23:10 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:23:10 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:23:10 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:23:10 | INFO     | core.base_scraper:update_scraping_job:138 - Job 88 atualizado: completed
2025-06-03 23:23:10 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:23:10 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:23:10 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:23:10 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:23:10 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 89
2025-06-03 23:23:11 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:23:11 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:23:18 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:23:18 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:23:18 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:23:28 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:23:29 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:23:29 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:23:35 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:23:35 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:23:35 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:23:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:23:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:23:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:23:54 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:23:54 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:23:54 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:24:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:24:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:24:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:24:08 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:24:08 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:24:08 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:24:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:24:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:24:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:24:14 | INFO     | core.base_scraper:update_scraping_job:138 - Job 89 atualizado: completed
2025-06-03 23:24:14 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:24:14 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:24:14 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:24:14 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:24:14 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 90
2025-06-03 23:24:15 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:24:15 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:24:17 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:24:17 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:24:17 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:24:17 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:24:17 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:24:17 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:24:17 | INFO     | core.base_scraper:update_scraping_job:138 - Job 90 atualizado: completed
2025-06-03 23:24:17 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:24:17 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:24:17 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:24:17 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:24:19 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:24:19 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:24:19 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:24:19 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:24:21 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:24:21 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:24:21 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:24:21 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:24:21 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:24:21 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 91
2025-06-03 23:24:21 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:24:21 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:24:22 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:24:22 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:24:22 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:24:22 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:24:23 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:24:23 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:24:23 | INFO     | core.base_scraper:update_scraping_job:138 - Job 91 atualizado: completed
2025-06-03 23:24:23 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:24:23 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:24:23 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:24:23 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:24:23 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 92
2025-06-03 23:24:24 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:24:24 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:24:52 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:24:52 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:24:59 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:24:59 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:24:59 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:25:27 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 23:25:27 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 23:25:33 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:25:33 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:25:33 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:25:39 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:25:39 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:25:39 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:25:44 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:25:45 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:25:45 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:25:51 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:25:51 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:25:51 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:25:56 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:25:56 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:25:56 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:25:56 | INFO     | core.base_scraper:update_scraping_job:138 - Job 92 atualizado: completed
2025-06-03 23:25:56 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 2 erros
2025-06-03 23:25:56 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:25:56 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:25:56 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:25:56 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 93
2025-06-03 23:25:57 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:25:57 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:25:57 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:25:57 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:25:57 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:25:57 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:25:57 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:25:57 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:25:57 | INFO     | core.base_scraper:update_scraping_job:138 - Job 93 atualizado: completed
2025-06-03 23:25:57 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:25:58 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:25:58 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:25:58 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:26:00 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:26:00 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:26:00 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:26:00 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:26:01 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:26:01 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:26:01 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:26:01 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:26:01 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:26:01 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 94
2025-06-03 23:26:02 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:26:02 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:26:02 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:26:02 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:26:03 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:26:03 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:26:03 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:26:03 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:26:03 | INFO     | core.base_scraper:update_scraping_job:138 - Job 94 atualizado: completed
2025-06-03 23:26:03 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:26:03 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:26:03 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:26:03 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:26:03 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 95
2025-06-03 23:26:04 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:26:04 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:26:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:26:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:26:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:26:22 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:26:22 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:26:22 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:26:51 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 23:26:51 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/tablet: Timeout 30000ms exceeded.
2025-06-03 23:26:56 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:26:56 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:26:56 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:27:04 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:27:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:27:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:27:11 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:27:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:27:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:27:17 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:27:17 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:27:17 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:27:26 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:27:26 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:27:26 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:27:26 | INFO     | core.base_scraper:update_scraping_job:138 - Job 95 atualizado: completed
2025-06-03 23:27:26 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:27:26 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:27:26 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:27:26 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:27:26 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 96
2025-06-03 23:27:27 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:27:27 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:27:29 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:27:29 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:27:29 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:27:29 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:27:29 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:27:29 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:27:29 | INFO     | core.base_scraper:update_scraping_job:138 - Job 96 atualizado: completed
2025-06-03 23:27:29 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:27:30 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:27:30 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:27:30 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:27:32 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:27:32 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:27:32 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:27:32 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:27:33 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:27:33 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:27:33 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:27:33 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:27:33 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:27:33 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 97
2025-06-03 23:27:34 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:27:34 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:27:35 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:27:35 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:27:35 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:27:35 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:27:35 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:27:35 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:27:35 | INFO     | core.base_scraper:update_scraping_job:138 - Job 97 atualizado: completed
2025-06-03 23:27:35 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:27:36 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:27:36 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:27:36 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:27:36 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 98
2025-06-03 23:27:36 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:27:36 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:28:05 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:28:05 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/smartphone: Timeout 30000ms exceeded.
2025-06-03 23:28:08 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:28:08 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:28:08 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:28:08 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:28:08 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 99
2025-06-03 23:28:08 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:28:08 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:28:11 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:28:11 | INFO     | services.drogasil_scraper:scrape:53 - 🔍 Tentando extrair dados reais do site...
2025-06-03 23:28:21 | WARNING  | services.drogasil_scraper:scrape:69 - Não foi possível extrair dados reais: Timeout 10000ms exceeded.
2025-06-03 23:28:21 | INFO     | services.drogasil_scraper:scrape:72 - 📄 Usando template HTML como fallback...
2025-06-03 23:28:21 | INFO     | services.drogasil_scraper:load_template_for_testing:40 - Template HTML carregado para testes
2025-06-03 23:28:21 | INFO     | services.drogasil_scraper:scrape:84 - Encontrados 1 produtos no template
2025-06-03 23:28:20 | INFO     | services.drogasil_scraper:scrape:92 - Produto do template extraído: Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico
2025-06-03 23:28:20 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico
2025-06-03 23:28:20 | INFO     | core.base_scraper:update_scraping_job:138 - Job 99 atualizado: completed
2025-06-03 23:28:20 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 1 itens, 0 erros
2025-06-03 23:28:20 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:28:20 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:28:33 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/notebook: Timeout 30000ms exceeded.
2025-06-03 23:28:33 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/notebook: Timeout 30000ms exceeded.
2025-06-03 23:28:40 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:28:40 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:28:40 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:28:50 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:28:50 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:28:50 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:28:56 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:28:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:28:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:29:00 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:29:00 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:29:00 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:29:00 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:29:00 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 100
2025-06-03 23:29:01 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:29:01 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:29:03 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:29:03 | INFO     | services.drogasil_scraper:scrape:53 - 🔍 Tentando extrair dados reais do site...
2025-06-03 23:29:04 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:29:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:29:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:29:10 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:29:10 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:29:10 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:29:13 | WARNING  | services.drogasil_scraper:scrape:69 - Não foi possível extrair dados reais: Timeout 10000ms exceeded.
2025-06-03 23:29:13 | INFO     | services.drogasil_scraper:scrape:72 - 📄 Usando template HTML como fallback...
2025-06-03 23:29:13 | INFO     | services.drogasil_scraper:load_template_for_testing:40 - Template HTML carregado para testes
2025-06-03 23:29:13 | INFO     | services.drogasil_scraper:scrape:84 - Encontrados 1 produtos no template
2025-06-03 23:29:13 | INFO     | services.drogasil_scraper:scrape:92 - Produto do template extraído: Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico
2025-06-03 23:29:13 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico
2025-06-03 23:29:13 | INFO     | core.base_scraper:update_scraping_job:138 - Job 100 atualizado: completed
2025-06-03 23:29:13 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 1 itens, 0 erros
2025-06-03 23:29:13 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:29:13 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:29:18 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:29:18 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:29:18 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:29:18 | INFO     | core.base_scraper:update_scraping_job:138 - Job 98 atualizado: completed
2025-06-03 23:29:18 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 2 erros
2025-06-03 23:29:19 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:29:19 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:29:19 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:29:19 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 101
2025-06-03 23:29:19 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:29:19 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:29:21 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:29:21 | INFO     | services.drogasil_scraper:scrape:24 - ⚠️ Usando dados simulados devido a proteções anti-bot do site
2025-06-03 23:29:21 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:29:21 | INFO     | services.drogasil_scraper:scrape:71 - Produto simulado extraído: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:29:21 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg EMS - 10 Comprimidos
2025-06-03 23:29:21 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Sódica 500mg Genérico - 20 Comprimidos
2025-06-03 23:29:21 | INFO     | core.base_scraper:update_scraping_job:138 - Job 101 atualizado: completed
2025-06-03 23:29:21 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 2 itens, 0 erros
2025-06-03 23:29:22 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:29:22 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:29:22 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:29:24 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:29:24 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:29:24 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:29:24 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:29:25 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:29:25 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:29:25 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:29:25 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:29:25 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:29:25 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 102
2025-06-03 23:29:26 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:29:26 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:29:26 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:29:26 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:29:27 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:29:27 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:29:27 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:29:27 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:29:27 | INFO     | core.base_scraper:update_scraping_job:138 - Job 102 atualizado: completed
2025-06-03 23:29:27 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:29:27 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:29:27 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:29:27 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:29:27 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 103
2025-06-03 23:29:28 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:29:28 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:29:35 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:29:35 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:29:35 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:29:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:29:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:29:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:29:53 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:29:53 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:29:53 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:30:00 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:30:00 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:30:00 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:30:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:30:05 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:30:05 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:30:34 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/televisao: Timeout 30000ms exceeded.
2025-06-03 23:30:34 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/televisao: Timeout 30000ms exceeded.
2025-06-03 23:30:39 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:30:39 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:30:39 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:30:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:30:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:30:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:30:46 | INFO     | core.base_scraper:update_scraping_job:138 - Job 103 atualizado: completed
2025-06-03 23:30:46 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:30:46 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:30:46 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:30:46 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:30:46 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 104
2025-06-03 23:30:47 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:30:47 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:30:49 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:30:49 | INFO     | services.drogasil_scraper:scrape:53 - 🔍 Tentando extrair dados reais do site...
2025-06-03 23:30:59 | WARNING  | services.drogasil_scraper:scrape:69 - Não foi possível extrair dados reais: Timeout 10000ms exceeded.
2025-06-03 23:30:59 | INFO     | services.drogasil_scraper:scrape:72 - 📄 Usando template HTML como fallback...
2025-06-03 23:30:59 | INFO     | services.drogasil_scraper:load_template_for_testing:40 - Template HTML carregado para testes
2025-06-03 23:30:59 | INFO     | services.drogasil_scraper:scrape:84 - Encontrados 1 produtos no template
2025-06-03 23:31:00 | INFO     | services.drogasil_scraper:scrape:92 - Produto do template extraído: Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico
2025-06-03 23:31:00 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico
2025-06-03 23:31:00 | INFO     | core.base_scraper:update_scraping_job:138 - Job 104 atualizado: completed
2025-06-03 23:31:00 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 1 itens, 0 erros
2025-06-03 23:31:00 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:31:00 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:31:00 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:31:02 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:31:02 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:31:02 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:31:02 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:31:03 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:31:03 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:31:03 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:31:03 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:31:03 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:31:03 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 105
2025-06-03 23:31:04 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:31:04 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:31:04 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:31:04 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:31:05 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:31:05 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:31:05 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:31:05 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:31:05 | INFO     | core.base_scraper:update_scraping_job:138 - Job 105 atualizado: completed
2025-06-03 23:31:05 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:31:05 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:31:05 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:31:05 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:31:05 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 106
2025-06-03 23:31:06 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:31:06 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:31:11 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:31:11 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:31:11 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:31:17 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:31:17 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:31:17 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:31:23 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:31:23 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:31:23 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:31:30 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:31:30 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:31:30 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:31:41 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:31:41 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:31:41 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:31:46 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:31:46 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:31:46 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:31:51 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:31:51 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:31:51 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:31:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:31:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:31:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:31:58 | INFO     | core.base_scraper:update_scraping_job:138 - Job 106 atualizado: completed
2025-06-03 23:31:58 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:31:59 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:31:59 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:31:59 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:31:59 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 107
2025-06-03 23:31:59 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:31:59 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:32:01 | INFO     | core.base_scraper:navigate_to_page:84 - Navegou para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:32:01 | INFO     | services.drogasil_scraper:scrape:53 - 🔍 Tentando extrair dados reais do site...
2025-06-03 23:32:10 | WARNING  | services.drogasil_scraper:scrape:69 - Não foi possível extrair dados reais: Timeout 10000ms exceeded.
2025-06-03 23:32:10 | INFO     | services.drogasil_scraper:scrape:72 - 📄 Usando template HTML como fallback...
2025-06-03 23:32:10 | INFO     | services.drogasil_scraper:load_template_for_testing:40 - Template HTML carregado para testes
2025-06-03 23:32:10 | INFO     | services.drogasil_scraper:scrape:84 - Encontrados 1 produtos no template
2025-06-03 23:32:10 | INFO     | services.drogasil_scraper:scrape:92 - Produto do template extraído: Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico
2025-06-03 23:32:10 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico
2025-06-03 23:32:10 | INFO     | core.base_scraper:update_scraping_job:138 - Job 107 atualizado: completed
2025-06-03 23:32:10 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 1 itens, 0 erros
2025-06-03 23:32:10 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:32:10 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:32:10 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:32:12 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:32:12 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:32:12 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:32:12 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:32:14 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:32:14 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:32:14 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:32:14 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:32:14 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:32:14 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 108
2025-06-03 23:32:14 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:32:14 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:32:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:32:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:32:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:32:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:32:16 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:32:16 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:32:16 | INFO     | core.base_scraper:update_scraping_job:138 - Job 108 atualizado: completed
2025-06-03 23:32:16 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:32:16 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:32:16 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:32:16 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:32:16 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 109
2025-06-03 23:32:17 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:32:17 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:32:26 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:32:26 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:32:26 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:32:31 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:32:31 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:32:31 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:32:42 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:32:42 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:32:42 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:32:49 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:32:49 | INFO     | services.drogasil_scraper:navigate_to_page:81 - 🌐 Navegando para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:32:49 | INFO     | services.drogasil_scraper:navigate_to_page:84 - 🏠 Visitando página inicial primeiro...
2025-06-03 23:32:50 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:32:50 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:32:50 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:33:01 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:33:01 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:33:01 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:33:07 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:33:07 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:33:07 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:33:12 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:33:12 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:33:12 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:33:17 | ERROR    | services.drogasil_scraper:navigate_to_page:117 - ❌ Erro ao navegar para https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:33:17 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:33:19 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:33:19 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:33:19 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:33:19 | INFO     | core.base_scraper:update_scraping_job:138 - Job 109 atualizado: completed
2025-06-03 23:33:19 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:33:20 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:33:20 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:33:20 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:33:20 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 110
2025-06-03 23:33:20 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:33:20 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:33:20 | INFO     | services.drogasil_scraper:navigate_to_page:81 - 🌐 Navegando para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:33:20 | INFO     | services.drogasil_scraper:navigate_to_page:84 - 🏠 Visitando página inicial primeiro...
2025-06-03 23:33:38 | INFO     | services.drogasil_scraper:navigate_to_page:89 - 🔍 Navegando para página de busca...
2025-06-03 23:33:47 | INFO     | services.drogasil_scraper:navigate_to_page:114 - ✅ Navegação concluída para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:33:47 | INFO     | services.drogasil_scraper:scrape:154 - 🔍 Extraindo dados reais do site...
2025-06-03 23:33:47 | INFO     | services.drogasil_scraper:scrape:158 - 📄 Título da página: Busca resultados para 7896004703398 | Drogasil
2025-06-03 23:33:47 | INFO     | services.drogasil_scraper:scrape:166 - ⏳ Aguardando JavaScript carregar...
2025-06-03 23:33:57 | INFO     | services.drogasil_scraper:scrape:188 - 🔍 Tentando seletor: article[data-item-id]
2025-06-03 23:33:57 | INFO     | services.drogasil_scraper:scrape:192 - ✅ Encontrados 1 elementos com article[data-item-id]
2025-06-03 23:33:57 | INFO     | services.drogasil_scraper:scrape:205 - 🔍 Extraindo dados do produto 1/1
2025-06-03 23:33:57 | INFO     | services.drogasil_scraper:scrape:209 - ✅ Produto extraído: Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico
2025-06-03 23:33:57 | INFO     | services.drogasil_scraper:scrape:216 - 📊 Total de produtos extraídos: 1
2025-06-03 23:33:57 | INFO     | core.base_scraper:save_scraped_data:152 - Dados salvos: Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico
2025-06-03 23:33:57 | INFO     | core.base_scraper:update_scraping_job:138 - Job 110 atualizado: completed
2025-06-03 23:33:57 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 1 itens, 0 erros
2025-06-03 23:33:57 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:33:57 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:33:57 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:33:59 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:33:59 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:33:59 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:33:59 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:34:00 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:34:00 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:34:00 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:34:00 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:34:00 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:34:00 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 111
2025-06-03 23:34:01 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:34:01 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:34:01 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:34:01 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:34:02 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:34:02 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:34:02 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:34:02 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:34:02 | INFO     | core.base_scraper:update_scraping_job:138 - Job 111 atualizado: completed
2025-06-03 23:34:02 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:34:02 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:34:02 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:34:02 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:34:02 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 112
2025-06-03 23:34:02 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:34:02 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:34:11 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:34:11 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:34:11 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:34:17 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:34:18 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:34:18 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:34:23 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:34:23 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:34:23 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:34:52 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 23:34:52 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/fone%20de%20ouvido: Timeout 30000ms exceeded.
2025-06-03 23:34:58 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:34:58 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:34:58 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:35:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:35:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:35:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:35:09 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:35:09 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:35:09 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:35:14 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:35:14 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:35:14 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:35:14 | INFO     | core.base_scraper:update_scraping_job:138 - Job 112 atualizado: completed
2025-06-03 23:35:14 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:35:14 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:35:14 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:35:14 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:35:14 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 113
2025-06-03 23:35:15 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:35:15 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:35:15 | INFO     | services.drogasil_scraper:navigate_to_page:81 - 🌐 Navegando para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:35:15 | INFO     | services.drogasil_scraper:navigate_to_page:84 - 🏠 Visitando página inicial primeiro...
2025-06-03 23:35:44 | ERROR    | services.drogasil_scraper:navigate_to_page:117 - ❌ Erro ao navegar para https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:35:44 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:35:44 | INFO     | core.base_scraper:update_scraping_job:138 - Job 113 atualizado: completed
2025-06-03 23:35:44 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:35:44 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:35:44 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:35:44 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:35:46 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:35:46 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:35:46 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:35:46 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:35:47 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:35:47 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:35:47 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:35:47 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:35:47 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:35:47 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 114
2025-06-03 23:35:48 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:35:48 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:35:48 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:35:48 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:35:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:35:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:35:49 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:35:49 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:35:49 | INFO     | core.base_scraper:update_scraping_job:138 - Job 114 atualizado: completed
2025-06-03 23:35:49 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:35:49 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:35:49 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:35:49 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:35:49 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 115
2025-06-03 23:35:50 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:35:50 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:35:57 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:35:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:35:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:36:04 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:36:04 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:36:04 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:36:11 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:36:11 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:36:11 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:36:17 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:36:18 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:36:18 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:36:25 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:36:25 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:36:25 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:36:25 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:36:25 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:36:25 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:36:25 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:36:25 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 116
2025-06-03 23:36:26 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:36:26 | INFO     | services.drogasil_scraper:setup_browser:39 - 🎭 User Agent selecionado: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Ap...
2025-06-03 23:36:26 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:36:26 | INFO     | services.drogasil_scraper:navigate_to_page:122 - 🌐 Navegando para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:36:26 | INFO     | services.drogasil_scraper:navigate_to_page:125 - 🏠 Visitando página inicial primeiro...
2025-06-03 23:36:31 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:36:31 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:36:31 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:36:55 | ERROR    | services.drogasil_scraper:navigate_to_page:131 - ❌ Erro na página inicial: Timeout 30000ms exceeded.
2025-06-03 23:36:57 | INFO     | services.drogasil_scraper:capture_error_screenshot:195 - 📸 Screenshot de erro salvo: error_erro_pagina_inicial_20250603_233655.png
2025-06-03 23:36:58 | INFO     | services.drogasil_scraper:capture_error_screenshot:202 - 💾 HTML de erro salvo: error_erro_pagina_inicial_20250603_233655.html
2025-06-03 23:36:58 | ERROR    | services.drogasil_scraper:navigate_to_page:184 - ❌ Erro ao navegar para https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:37:00 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 23:37:00 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 23:37:00 | INFO     | services.drogasil_scraper:capture_error_screenshot:195 - 📸 Screenshot de erro salvo: error_erro_navegacao_final_20250603_233658.png
2025-06-03 23:37:00 | INFO     | services.drogasil_scraper:capture_error_screenshot:202 - 💾 HTML de erro salvo: error_erro_navegacao_final_20250603_233658.html
2025-06-03 23:37:00 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:37:00 | INFO     | core.base_scraper:update_scraping_job:138 - Job 116 atualizado: completed
2025-06-03 23:37:00 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:37:00 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:37:00 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:37:09 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:37:09 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:37:09 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:37:09 | INFO     | core.base_scraper:update_scraping_job:138 - Job 115 atualizado: completed
2025-06-03 23:37:09 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:37:09 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:37:09 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:37:09 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:37:09 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 117
2025-06-03 23:37:10 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:37:10 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:37:10 | INFO     | services.drogasil_scraper:navigate_to_page:81 - 🌐 Navegando para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:37:10 | INFO     | services.drogasil_scraper:navigate_to_page:84 - 🏠 Visitando página inicial primeiro...
2025-06-03 23:37:39 | ERROR    | services.drogasil_scraper:navigate_to_page:117 - ❌ Erro ao navegar para https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:37:39 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:37:39 | INFO     | core.base_scraper:update_scraping_job:138 - Job 117 atualizado: completed
2025-06-03 23:37:39 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:37:39 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:37:39 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:37:39 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:37:41 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:37:41 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:37:41 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:37:41 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:37:42 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:37:42 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:37:42 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:37:42 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:37:42 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:37:42 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 118
2025-06-03 23:37:43 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:37:43 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:37:43 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:37:43 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:37:44 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:37:44 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:37:44 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:37:44 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:37:44 | INFO     | core.base_scraper:update_scraping_job:138 - Job 118 atualizado: completed
2025-06-03 23:37:44 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:37:45 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:37:45 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:37:45 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:37:45 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 119
2025-06-03 23:37:45 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:37:45 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:37:51 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:37:51 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:37:51 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:38:00 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:38:00 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:38:00 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:38:03 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:38:03 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:38:03 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:38:03 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:38:03 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 120
2025-06-03 23:38:04 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:38:04 | INFO     | services.drogasil_scraper:setup_browser:39 - 🎭 User Agent selecionado: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0...
2025-06-03 23:38:04 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:38:04 | INFO     | services.drogasil_scraper:navigate_to_page:122 - 🌐 Navegando para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:38:04 | INFO     | services.drogasil_scraper:navigate_to_page:125 - 🔍 Navegando diretamente para página de busca...
2025-06-03 23:38:06 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:38:06 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:38:06 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:38:07 | INFO     | services.drogasil_scraper:navigate_to_page:130 - ✅ Página de busca carregada
2025-06-03 23:38:07 | INFO     | services.drogasil_scraper:navigate_to_page:139 - 📄 Título da página: Access Denied
2025-06-03 23:38:07 | ERROR    | services.drogasil_scraper:navigate_to_page:142 - 🚫 Acesso negado detectado
2025-06-03 23:38:07 | INFO     | services.drogasil_scraper:capture_error_screenshot:184 - 📸 Screenshot de erro salvo: error_access_denied_20250603_233807.png
2025-06-03 23:38:07 | INFO     | services.drogasil_scraper:capture_error_screenshot:191 - 💾 HTML de erro salvo: error_access_denied_20250603_233807.html
2025-06-03 23:38:07 | ERROR    | services.drogasil_scraper:navigate_to_page:173 - ❌ Erro ao navegar para https://www.drogasil.com.br/search?w=7896004703398: Site bloqueou o acesso - Access Denied
2025-06-03 23:38:07 | INFO     | services.drogasil_scraper:capture_error_screenshot:184 - 📸 Screenshot de erro salvo: error_erro_navegacao_final_20250603_233807.png
2025-06-03 23:38:07 | INFO     | services.drogasil_scraper:capture_error_screenshot:191 - 💾 HTML de erro salvo: error_erro_navegacao_final_20250603_233807.html
2025-06-03 23:38:07 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.drogasil.com.br/search?w=7896004703398: Site bloqueou o acesso - Access Denied
2025-06-03 23:38:07 | INFO     | core.base_scraper:update_scraping_job:138 - Job 120 atualizado: completed
2025-06-03 23:38:07 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:38:08 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:38:08 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:38:15 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:38:16 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:38:16 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:38:20 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:38:20 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:38:20 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:38:27 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:38:27 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:38:27 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:38:56 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 23:38:56 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 23:39:01 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:39:02 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:39:02 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:39:02 | INFO     | core.base_scraper:update_scraping_job:138 - Job 119 atualizado: completed
2025-06-03 23:39:02 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:39:02 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:39:02 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:39:02 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:39:02 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 121
2025-06-03 23:39:02 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:39:02 | INFO     | services.drogasil_scraper:setup_browser:39 - 🎭 User Agent selecionado: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0...
2025-06-03 23:39:02 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:39:02 | INFO     | services.drogasil_scraper:navigate_to_page:122 - 🌐 Navegando para: https://www.drogasil.com.br/search?w=7896004703398
2025-06-03 23:39:02 | INFO     | services.drogasil_scraper:navigate_to_page:125 - 🏠 Visitando página inicial primeiro...
2025-06-03 23:39:06 | INFO     | services.drogasil_scraper:navigate_to_page:129 - ✅ Página inicial carregada
2025-06-03 23:39:06 | INFO     | services.drogasil_scraper:navigate_to_page:137 - 🔍 Navegando para página de busca...
2025-06-03 23:39:08 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:39:08 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:39:08 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:39:08 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:39:08 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 122
2025-06-03 23:39:08 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:39:08 | INFO     | services.drogasil_scraper:setup_browser:39 - 🎭 User Agent selecionado: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Ap...
2025-06-03 23:39:08 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:39:08 | INFO     | services.drogasil_scraper:navigate_to_page:122 - 🌐 Simulando navegação humana para buscar EAN...
2025-06-03 23:39:08 | INFO     | services.drogasil_scraper:navigate_to_page:126 - 🔍 EAN a buscar: 7896004703398
2025-06-03 23:39:08 | INFO     | services.drogasil_scraper:navigate_to_page:129 - 🏠 Visitando página inicial...
2025-06-03 23:39:11 | INFO     | services.drogasil_scraper:navigate_to_page:133 - ✅ Página inicial carregada
2025-06-03 23:39:11 | INFO     | services.drogasil_scraper:navigate_to_page:140 - 🔍 Procurando campo de busca...
2025-06-03 23:39:12 | WARNING  | services.drogasil_scraper:navigate_to_page:164 - ⚠️ Campo de busca não encontrado, tentando URL direta...
2025-06-03 23:39:12 | INFO     | services.drogasil_scraper:navigate_to_page:141 - ✅ Página de busca carregada
2025-06-03 23:39:12 | INFO     | services.drogasil_scraper:navigate_to_page:150 - 📄 Título da página: Access Denied
2025-06-03 23:39:12 | ERROR    | services.drogasil_scraper:navigate_to_page:153 - 🚫 Acesso negado detectado
2025-06-03 23:39:12 | INFO     | services.drogasil_scraper:capture_error_screenshot:195 - 📸 Screenshot de erro salvo: error_access_denied_20250603_233912.png
2025-06-03 23:39:12 | INFO     | services.drogasil_scraper:capture_error_screenshot:202 - 💾 HTML de erro salvo: error_access_denied_20250603_233912.html
2025-06-03 23:39:12 | ERROR    | services.drogasil_scraper:navigate_to_page:184 - ❌ Erro ao navegar para https://www.drogasil.com.br/search?w=7896004703398: Site bloqueou o acesso - Access Denied
2025-06-03 23:39:12 | INFO     | services.drogasil_scraper:capture_error_screenshot:195 - 📸 Screenshot de erro salvo: error_erro_navegacao_final_20250603_233912.png
2025-06-03 23:39:12 | INFO     | services.drogasil_scraper:capture_error_screenshot:202 - 💾 HTML de erro salvo: error_erro_navegacao_final_20250603_233912.html
2025-06-03 23:39:12 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.drogasil.com.br/search?w=7896004703398: Site bloqueou o acesso - Access Denied
2025-06-03 23:39:12 | INFO     | core.base_scraper:update_scraping_job:138 - Job 121 atualizado: completed
2025-06-03 23:39:12 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:39:12 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:39:12 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:39:12 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:39:14 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:39:14 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:39:14 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:39:14 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:39:15 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:39:15 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:39:15 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:39:15 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:39:15 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:39:15 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 123
2025-06-03 23:39:16 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:39:16 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:39:17 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:39:17 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:39:15 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:39:15 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:39:16 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:39:16 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:39:16 | INFO     | core.base_scraper:update_scraping_job:138 - Job 123 atualizado: completed
2025-06-03 23:39:16 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:39:16 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:39:16 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:39:16 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:39:16 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 124
2025-06-03 23:39:17 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:39:17 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:39:23 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:39:23 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:39:23 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:39:29 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:39:29 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:39:29 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:39:36 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:39:36 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:39:36 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:39:44 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:39:44 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:39:44 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:39:51 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:39:51 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:39:51 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:39:59 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:39:59 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:39:59 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:40:05 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/geladeira
2025-06-03 23:40:05 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:40:05 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:40:10 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:40:10 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:40:10 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:40:10 | INFO     | core.base_scraper:update_scraping_job:138 - Job 124 atualizado: completed
2025-06-03 23:40:10 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 0 erros
2025-06-03 23:40:11 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:40:11 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:40:11 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:40:11 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 125
2025-06-03 23:40:11 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:40:11 | INFO     | services.drogasil_scraper:setup_browser:39 - 🎭 User Agent selecionado: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0...
2025-06-03 23:40:11 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:40:11 | INFO     | services.drogasil_scraper:navigate_to_page:122 - 🌐 Simulando navegação humana para buscar EAN...
2025-06-03 23:40:11 | INFO     | services.drogasil_scraper:navigate_to_page:126 - 🔍 EAN a buscar: 7896004703398
2025-06-03 23:40:11 | INFO     | services.drogasil_scraper:navigate_to_page:129 - 🏠 Visitando página inicial...
2025-06-03 23:40:13 | INFO     | services.drogasil_scraper:navigate_to_page:133 - ✅ Página inicial carregada
2025-06-03 23:40:13 | INFO     | services.drogasil_scraper:navigate_to_page:140 - 🔍 Procurando campo de busca...
2025-06-03 23:40:13 | WARNING  | services.drogasil_scraper:navigate_to_page:164 - ⚠️ Campo de busca não encontrado, tentando URL direta...
2025-06-03 23:40:16 | INFO     | services.drogasil_scraper:navigate_to_page:218 - 📄 Título da página: Access Denied
2025-06-03 23:40:16 | ERROR    | services.drogasil_scraper:navigate_to_page:221 - 🚫 Acesso negado detectado
2025-06-03 23:40:16 | INFO     | services.drogasil_scraper:capture_error_screenshot:263 - 📸 Screenshot de erro salvo: error_access_denied_20250603_234016.png
2025-06-03 23:40:16 | INFO     | services.drogasil_scraper:capture_error_screenshot:270 - 💾 HTML de erro salvo: error_access_denied_20250603_234016.html
2025-06-03 23:40:16 | ERROR    | services.drogasil_scraper:navigate_to_page:252 - ❌ Erro ao navegar para https://www.drogasil.com.br/search?w=7896004703398: Site bloqueou o acesso - Access Denied
2025-06-03 23:40:17 | INFO     | services.drogasil_scraper:capture_error_screenshot:263 - 📸 Screenshot de erro salvo: error_erro_navegacao_final_20250603_234016.png
2025-06-03 23:40:17 | INFO     | services.drogasil_scraper:capture_error_screenshot:270 - 💾 HTML de erro salvo: error_erro_navegacao_final_20250603_234016.html
2025-06-03 23:40:17 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.drogasil.com.br/search?w=7896004703398: Site bloqueou o acesso - Access Denied
2025-06-03 23:40:17 | INFO     | core.base_scraper:update_scraping_job:138 - Job 125 atualizado: completed
2025-06-03 23:40:17 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:40:17 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:40:17 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:40:17 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:40:19 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:40:19 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:40:19 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:40:19 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:40:20 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:40:20 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:40:20 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:40:20 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:40:20 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:40:20 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 126
2025-06-03 23:40:21 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:40:21 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:40:21 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:40:21 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:40:21 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:40:21 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:40:22 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:40:22 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:40:22 | INFO     | core.base_scraper:update_scraping_job:138 - Job 126 atualizado: completed
2025-06-03 23:40:22 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:40:22 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:40:22 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:40:22 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:40:22 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 127
2025-06-03 23:40:23 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:40:23 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:40:29 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:40:30 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:40:30 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:40:36 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:40:36 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:40:36 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:40:44 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:40:44 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:40:44 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:40:52 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:40:52 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:40:52 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:41:03 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:41:03 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:41:03 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:41:08 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/televisao
2025-06-03 23:41:08 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:41:08 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:41:37 | ERROR    | services.mercadolivre_scraper:navigate_to_page:266 - Erro ao navegar para https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 23:41:37 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://lista.mercadolivre.com.br/geladeira: Timeout 30000ms exceeded.
2025-06-03 23:41:41 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/micro-ondas
2025-06-03 23:41:41 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:41:41 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:41:41 | INFO     | core.base_scraper:update_scraping_job:138 - Job 127 atualizado: completed
2025-06-03 23:41:41 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:41:41 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para mercadolivre
2025-06-03 23:41:41 | INFO     | __main__:run_scraper:56 - Scraper mercadolivre concluído
2025-06-03 23:41:41 | INFO     | __main__:run_scraper:54 - Iniciando scraper: drogasil
2025-06-03 23:41:41 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 128
2025-06-03 23:41:41 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para drogasil
2025-06-03 23:41:41 | INFO     | services.drogasil_scraper:setup_browser:39 - 🎭 User Agent selecionado: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb...
2025-06-03 23:41:41 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 1 URLs
2025-06-03 23:41:41 | INFO     | services.drogasil_scraper:navigate_to_page:122 - 🌐 Simulando navegação humana para buscar EAN...
2025-06-03 23:41:41 | INFO     | services.drogasil_scraper:navigate_to_page:126 - 🔍 EAN a buscar: 7896004703398
2025-06-03 23:41:41 | INFO     | services.drogasil_scraper:navigate_to_page:129 - 🏠 Visitando página inicial...
2025-06-03 23:41:49 | INFO     | services.drogasil_scraper:navigate_to_page:133 - ✅ Página inicial carregada
2025-06-03 23:41:49 | INFO     | services.drogasil_scraper:navigate_to_page:140 - 🔍 Procurando campo de busca...
2025-06-03 23:41:51 | INFO     | services.drogasil_scraper:navigate_to_page:158 - ✅ Campo de busca encontrado: input[placeholder*="busca"]
2025-06-03 23:41:51 | INFO     | services.drogasil_scraper:navigate_to_page:170 - ⌨️ Digitando EAN: 7896004703398
2025-06-03 23:41:54 | INFO     | services.drogasil_scraper:navigate_to_page:180 - 🔍 Procurando botão de busca...
2025-06-03 23:41:54 | INFO     | services.drogasil_scraper:navigate_to_page:195 - ✅ Botão de busca encontrado: button:has-text("Buscar")
2025-06-03 23:42:23 | ERROR    | services.drogasil_scraper:navigate_to_page:212 - ❌ Erro na busca: Timeout 30000ms exceeded.
2025-06-03 23:42:25 | INFO     | services.drogasil_scraper:capture_error_screenshot:263 - 📸 Screenshot de erro salvo: error_erro_busca_20250603_234223.png
2025-06-03 23:42:25 | INFO     | services.drogasil_scraper:capture_error_screenshot:270 - 💾 HTML de erro salvo: error_erro_busca_20250603_234223.html
2025-06-03 23:42:25 | ERROR    | services.drogasil_scraper:navigate_to_page:252 - ❌ Erro ao navegar para https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:42:27 | INFO     | services.drogasil_scraper:capture_error_screenshot:263 - 📸 Screenshot de erro salvo: error_erro_navegacao_final_20250603_234225.png
2025-06-03 23:42:27 | INFO     | services.drogasil_scraper:capture_error_screenshot:270 - 💾 HTML de erro salvo: error_erro_navegacao_final_20250603_234225.html
2025-06-03 23:42:27 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://www.drogasil.com.br/search?w=7896004703398: Timeout 30000ms exceeded.
2025-06-03 23:42:27 | INFO     | core.base_scraper:update_scraping_job:138 - Job 128 atualizado: completed
2025-06-03 23:42:27 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 1 erros
2025-06-03 23:42:27 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para drogasil
2025-06-03 23:42:27 | INFO     | __main__:run_scraper:56 - Scraper drogasil concluído
2025-06-03 23:42:27 | INFO     | __main__:run_all_scrapers:68 - Execução de todos os scrapers concluída
2025-06-03 23:42:29 | INFO     | __main__:main:117 - Inicializando sistema...
2025-06-03 23:42:29 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:42:29 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:42:29 | INFO     | __main__:main:119 - Sistema inicializado com sucesso!
2025-06-03 23:42:30 | INFO     | __main__:init_database:73 - Inicializando banco de dados...
2025-06-03 23:42:30 | INFO     | __main__:init_database:75 - Banco de dados inicializado com sucesso
2025-06-03 23:42:30 | INFO     | __main__:register_scrapers:40 - Scrapers registrados: ['example', 'mercadolivre', 'drogasil']
2025-06-03 23:42:30 | INFO     | __main__:run_all_scrapers:60 - Iniciando execução de todos os scrapers
2025-06-03 23:42:30 | INFO     | __main__:run_scraper:54 - Iniciando scraper: example
2025-06-03 23:42:30 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 129
2025-06-03 23:42:31 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para example_site
2025-06-03 23:42:31 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 3 URLs
2025-06-03 23:42:32 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:42:32 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/eletronicos: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/eletronicos
2025-06-03 23:42:32 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:42:32 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/roupas: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/roupas
2025-06-03 23:42:32 | ERROR    | core.base_scraper:navigate_to_page:87 - Erro ao navegar para https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:42:32 | ERROR    | core.base_scraper:run_scraping:191 - Erro ao processar https://example-ecommerce.com/categoria/casa: net::ERR_CERT_COMMON_NAME_INVALID at https://example-ecommerce.com/categoria/casa
2025-06-03 23:42:32 | INFO     | core.base_scraper:update_scraping_job:138 - Job 129 atualizado: completed
2025-06-03 23:42:32 | INFO     | core.base_scraper:run_scraping:194 - Scraping concluído: 0 itens, 3 erros
2025-06-03 23:42:33 | INFO     | core.base_scraper:cleanup:60 - Recursos limpos para example_site
2025-06-03 23:42:33 | INFO     | __main__:run_scraper:56 - Scraper example concluído
2025-06-03 23:42:33 | INFO     | __main__:run_scraper:54 - Iniciando scraper: mercadolivre
2025-06-03 23:42:33 | INFO     | core.base_scraper:create_scraping_job:116 - Job de scraping criado: 130
2025-06-03 23:42:33 | INFO     | core.base_scraper:setup_browser:45 - Browser configurado para mercadolivre
2025-06-03 23:42:33 | INFO     | core.base_scraper:run_scraping:178 - Iniciando scraping de 8 URLs
2025-06-03 23:42:41 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartphone
2025-06-03 23:42:41 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:42:41 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:42:49 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/notebook
2025-06-03 23:42:49 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:42:49 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:42:57 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/tablet
2025-06-03 23:42:57 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:42:57 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:43:09 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/fone%20de%20ouvido
2025-06-03 23:43:09 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:43:09 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
2025-06-03 23:43:15 | INFO     | services.mercadolivre_scraper:navigate_to_page:263 - Navegou para: https://lista.mercadolivre.com.br/smartwatch
2025-06-03 23:43:15 | INFO     | services.mercadolivre_scraper:scrape:48 - Encontrados 0 produtos na página
2025-06-03 23:43:15 | INFO     | services.mercadolivre_scraper:scrape:59 - Extraídos 0 produtos válidos
