#!/usr/bin/env python3
"""
Teste com o EAN real do Drogasil: 7896004703398
"""

import asyncio
from services.drogasil_scraper import DrogasilScraper

async def test_real_drogasil():
    """Testa com o EAN real do Drogasil"""
    
    # EAN real que existe no Drogasil
    real_ean = "7896004703398"
    
    print(f"🔍 Testando EAN real do Drogasil: {real_ean}")
    print("=" * 50)
    
    scraper = DrogasilScraper(eans=[real_ean])
    
    try:
        async with scraper:
            url = f"https://www.drogasil.com.br/search?w={real_ean}"
            print(f"📍 URL: {url}")
            
            await scraper.navigate_to_page(url)
            print("⏳ Aguardando página carregar...")
            await scraper.page.wait_for_timeout(10000)  # Aguardar JavaScript
            
            # Verificar título
            title = await scraper.page.title()
            print(f"📄 Título: {title}")
            
            # Verificar se há produtos
            page_text = await scraper.page.inner_text('body')
            
            if 'nenhum resultado' in page_text.lower():
                print("❌ Nenhum resultado encontrado")
                return
            elif 'R$' in page_text:
                print("✅ Produtos encontrados (contém preços)!")
                
                # Contar preços
                price_count = page_text.count('R$')
                print(f"💰 Encontrados {price_count} preços na página")
                
                # Verificar se encontra os elementos esperados
                print("\n🔍 Verificando elementos esperados:")
                
                # Verificar artigos de produto
                articles = await scraper.page.query_selector_all('article[data-item-id]')
                print(f"   📦 Artigos de produto: {len(articles)}")
                
                if articles:
                    print(f"   ✅ Encontrados {len(articles)} produtos!")
                    
                    # Testar extração de dados
                    print("\n📊 Testando extração de dados:")
                    products = await scraper.scrape()
                    
                    if products:
                        print(f"✅ Extraídos {len(products)} produtos com sucesso!")
                        
                        for i, product in enumerate(products, 1):
                            print(f"\n📦 Produto {i}:")
                            print(f"   📝 Nome: {product.get('product_name', 'N/A')}")
                            print(f"   💰 Preço: R$ {product.get('price', 'N/A')}")
                            print(f"   🏷️ Preço Original: R$ {product.get('original_price', 'N/A')}")
                            print(f"   📊 Desconto: {product.get('discount_percentage', 'N/A')}%")
                            print(f"   🔗 URL: {product.get('url', 'N/A')}")
                            print(f"   🖼️ Imagem: {product.get('image_url', 'N/A')[:50]}...")
                            print(f"   🏭 Marca: {product.get('brand', 'N/A')}")
                            print(f"   ✅ Disponível: {product.get('availability', 'N/A')}")
                            print(f"   ⭐ Rating: {product.get('rating', 'N/A')}")
                            print(f"   📝 Avaliações: {product.get('reviews_count', 'N/A')}")
                            
                            # Dados adicionais
                            additional = product.get('additional_data', {})
                            print(f"   🔍 EAN Buscado: {additional.get('ean_searched', 'N/A')}")
                    else:
                        print("❌ Nenhum produto foi extraído")
                        
                        # Debug: verificar por que não extraiu
                        print("\n🔍 Debug - verificando elementos individuais:")
                        
                        first_article = articles[0]
                        
                        # Testar nome
                        name_element = await first_article.query_selector('h2 a')
                        if name_element:
                            name = await name_element.inner_text()
                            print(f"   📝 Nome encontrado: {name}")
                        else:
                            print("   ❌ Nome não encontrado")
                        
                        # Testar preço
                        price_element = await first_article.query_selector('[data-testid="price"]')
                        if price_element:
                            price_text = await price_element.inner_text()
                            print(f"   💰 Preço encontrado: {price_text}")
                        else:
                            print("   ❌ Preço não encontrado")
                        
                        # Testar imagem
                        img_element = await first_article.query_selector('[data-testid="product-image"]')
                        if img_element:
                            img_src = await img_element.get_attribute('src')
                            print(f"   🖼️ Imagem encontrada: {img_src[:50]}...")
                        else:
                            print("   ❌ Imagem não encontrada")
                
                else:
                    print("   ❌ Nenhum artigo de produto encontrado")
                    
                    # Tentar outros seletores
                    print("\n🔍 Tentando outros seletores:")
                    test_selectors = [
                        'article',
                        '[data-item-id]',
                        '[class*="product"]',
                        '[class*="item"]'
                    ]
                    
                    for selector in test_selectors:
                        elements = await scraper.page.query_selector_all(selector)
                        print(f"   {selector}: {len(elements)} elementos")
                
                # Salvar HTML para análise
                content = await scraper.page.content()
                with open("test_real_drogasil.html", "w", encoding="utf-8") as f:
                    f.write(content)
                print(f"\n💾 HTML salvo como: test_real_drogasil.html")
                
            else:
                print("⚠️ Status desconhecido - página carregou mas sem preços")
                
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_real_drogasil())
