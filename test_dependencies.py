#!/usr/bin/env python3
"""
Script para testar se todas as dependências estão funcionando corretamente
"""

import sys
import importlib
from typing import List, <PERSON>ple

def test_import(module_name: str, package_name: str = None) -> Tuple[bool, str]:
    """Testa se um módulo pode ser importado"""
    try:
        importlib.import_module(module_name)
        return True, f"✅ {package_name or module_name}"
    except ImportError as e:
        return False, f"❌ {package_name or module_name}: {str(e)}"

def test_dependencies() -> List[Tuple[bool, str]]:
    """Testa todas as dependências principais"""
    dependencies = [
        # Core dependencies
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("pydantic", "Pydantic"),
        ("pydantic_settings", "Pydantic Settings"),
        
        # Database
        ("sqlalchemy", "SQLAlchemy"),
        ("psycopg2", "psycopg2-binary"),
        ("asyncpg", "AsyncPG"),
        ("alembic", "Alembic"),
        
        # Web scraping
        ("playwright", "Playwright"),
        ("bs4", "BeautifulSoup4"),
        ("httpx", "HTTPX"),
        
        # Utilities
        ("dotenv", "python-dotenv"),
        ("loguru", "Loguru"),
        ("schedule", "Schedule"),
        ("pandas", "Pandas"),
    ]
    
    results = []
    for module, package in dependencies:
        success, message = test_import(module, package)
        results.append((success, message))
    
    return results

def test_versions():
    """Testa versões específicas das dependências críticas"""
    print("\n🔍 Verificando versões críticas:")
    
    try:
        import fastapi
        print(f"   FastAPI: {fastapi.__version__}")
        
        import pydantic
        print(f"   Pydantic: {pydantic.__version__}")
        
        import uvicorn
        print(f"   Uvicorn: {uvicorn.__version__}")
        
        import sqlalchemy
        print(f"   SQLAlchemy: {sqlalchemy.__version__}")
        
        import playwright
        print(f"   Playwright: {playwright.__version__}")
        
    except Exception as e:
        print(f"   ❌ Erro ao verificar versões: {e}")

def test_compatibility():
    """Testa compatibilidade entre FastAPI e Pydantic"""
    print("\n🔗 Testando compatibilidade FastAPI + Pydantic:")
    
    try:
        from fastapi import FastAPI
        from pydantic import BaseModel
        
        # Teste básico de compatibilidade
        app = FastAPI()
        
        class TestModel(BaseModel):
            name: str
            value: int
        
        @app.get("/test")
        def test_endpoint():
            return {"status": "ok"}
        
        print("   ✅ FastAPI + Pydantic compatíveis")
        return True
        
    except Exception as e:
        print(f"   ❌ Incompatibilidade detectada: {e}")
        return False

def test_database_connection():
    """Testa se é possível criar conexão com banco"""
    print("\n🗄️ Testando conexão com banco:")
    
    try:
        from sqlalchemy import create_engine
        from database.models import Base
        
        # Teste com SQLite em memória
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        
        print("   ✅ Modelos de banco funcionando")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro nos modelos de banco: {e}")
        return False

def test_playwright_installation():
    """Testa se Playwright está instalado corretamente"""
    print("\n🎭 Testando Playwright:")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # Verifica se browsers estão instalados
            browsers = []
            try:
                p.chromium.launch(headless=True).close()
                browsers.append("Chromium")
            except:
                pass
            
            try:
                p.firefox.launch(headless=True).close()
                browsers.append("Firefox")
            except:
                pass
            
            try:
                p.webkit.launch(headless=True).close()
                browsers.append("WebKit")
            except:
                pass
            
            if browsers:
                print(f"   ✅ Browsers instalados: {', '.join(browsers)}")
                return True
            else:
                print("   ⚠️ Nenhum browser instalado. Execute: playwright install")
                return False
                
    except Exception as e:
        print(f"   ❌ Erro no Playwright: {e}")
        return False

def main():
    """Função principal"""
    print("🧪 TESTE DE DEPENDÊNCIAS - PRICENOW")
    print("=" * 50)
    
    # Teste de importações
    print("\n📦 Testando importações:")
    results = test_dependencies()
    
    success_count = 0
    for success, message in results:
        print(f"   {message}")
        if success:
            success_count += 1
    
    total_deps = len(results)
    print(f"\n📊 Resultado: {success_count}/{total_deps} dependências OK")
    
    # Testes adicionais
    test_versions()
    
    compatibility_ok = test_compatibility()
    database_ok = test_database_connection()
    playwright_ok = test_playwright_installation()
    
    # Resumo final
    print("\n" + "=" * 50)
    print("📋 RESUMO FINAL:")
    print(f"   Dependências: {success_count}/{total_deps}")
    print(f"   Compatibilidade: {'✅' if compatibility_ok else '❌'}")
    print(f"   Banco de dados: {'✅' if database_ok else '❌'}")
    print(f"   Playwright: {'✅' if playwright_ok else '❌'}")
    
    # Verifica se tudo está OK
    all_ok = (
        success_count == total_deps and
        compatibility_ok and
        database_ok and
        playwright_ok
    )
    
    if all_ok:
        print("\n🎉 TUDO FUNCIONANDO PERFEITAMENTE!")
        print("   Você pode executar: python main.py api")
        return 0
    else:
        print("\n⚠️ ALGUNS PROBLEMAS DETECTADOS")
        print("   Consulte: DEPENDENCIES-FIX.md")
        print("   Ou use Docker: docker-compose up -d")
        return 1

if __name__ == "__main__":
    sys.exit(main())
