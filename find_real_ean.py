#!/usr/bin/env python3
"""
Busca por produtos genéricos para encontrar EANs reais do Drogasil
"""

import asyncio
from services.drogasil_scraper import DrogasilScraper

async def find_real_ean():
    """Busca por produtos genéricos para encontrar EANs reais"""
    
    # Termos de busca genéricos
    search_terms = [
        "paracetamol",
        "dipirona", 
        "vitamina",
        "shampoo",
        "sabonete"
    ]
    
    for term in search_terms:
        print(f"\n🔍 Buscando por: {term}")
        print("-" * 40)
        
        scraper = DrogasilScraper(eans=[])
        
        try:
            async with scraper:
                # Buscar por termo genérico
                url = f"https://www.drogasil.com.br/search?w={term}"
                print(f"📍 URL: {url}")
                
                await scraper.navigate_to_page(url)
                await scraper.page.wait_for_timeout(10000)  # Aguardar JavaScript
                
                # Verificar título
                title = await scraper.page.title()
                print(f"📄 Título: {title}")
                
                # Verificar se há produtos
                page_text = await scraper.page.inner_text('body')
                
                if 'nenhum resultado' in page_text.lower():
                    print("❌ Nenhum resultado encontrado")
                elif 'R$' in page_text:
                    print("✅ Produtos encontrados!")
                    
                    # Contar preços
                    price_count = page_text.count('R$')
                    print(f"💰 Encontrados {price_count} preços na página")
                    
                    # Tentar encontrar seletores que funcionam
                    print("\n🔍 Procurando seletores que funcionam:")
                    
                    # Lista mais ampla de seletores
                    test_selectors = [
                        'div',
                        'span',
                        'a',
                        'p',
                        '[class*="price"]',
                        '[class*="produto"]',
                        '[class*="item"]',
                        '[class*="card"]',
                        '[id*="product"]',
                        '[data-*]'
                    ]
                    
                    for selector in test_selectors:
                        elements = await scraper.page.query_selector_all(selector)
                        if elements and len(elements) < 100:  # Evitar seletores muito genéricos
                            print(f"   ✅ {selector}: {len(elements)} elementos")
                            
                            # Se encontrou poucos elementos, pode ser produtos
                            if len(elements) <= 20:
                                print(f"      🔍 Analisando elementos de {selector}:")
                                for i, element in enumerate(elements[:3]):  # Apenas 3 primeiros
                                    try:
                                        text = await element.inner_text()
                                        if text and len(text.strip()) > 10:
                                            print(f"         {i+1}. {text[:100]}...")
                                    except:
                                        pass
                    
                    # Salvar HTML para análise manual
                    content = await scraper.page.content()
                    filename = f"drogasil_{term}.html"
                    with open(filename, "w", encoding="utf-8") as f:
                        f.write(content)
                    print(f"\n💾 HTML salvo como: {filename}")
                    
                    # Se encontrou produtos, para aqui para análise
                    break
                    
        except Exception as e:
            print(f"❌ Erro: {e}")

if __name__ == "__main__":
    asyncio.run(find_real_ean())
